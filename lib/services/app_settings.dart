// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get/get.dart';

import '../constants/layout_config.dart';

class AppSettings extends GetxController {
  final SharedPreferences _prefs;

  // 使用 Rx 变量替代普通变量
  final _locale = const Locale('zh', '').obs;
  final _themeMode = ThemeMode.light.obs;
  // final _fontFamily = 'MiSansNormal'.obs;
  final _colorScheme = FlexScheme.amber.obs;
  final _defaultDeleteDays = 15.obs;
  final _timelineLayout = TimelineLayout.double.obs;
  final _isInitialized = false.obs;

  // Getters for reactive variables
  Locale get locale => _locale.value;
  ThemeMode get themeMode => _themeMode.value;
  // String get fontFamily => _fontFamily.value;
  FlexScheme get colorScheme => _colorScheme.value;
  int get defaultDeleteDays => _defaultDeleteDays.value;
  TimelineLayout get timelineLayout => _timelineLayout.value;

  AppSettings(this._prefs) {
    _loadSettingsSync();
  }

  static const String _localeKey = 'locale';
  static const String _themeModeKey = 'themeMode';
  // static const String _fontFamilyKey = 'fontFamily';
  static const String _colorSchemeKey = 'colorScheme';
  static const String _timelineLayoutKey = 'timeline_layout';

  Future<bool> get isInitialized async {
    if (!_isInitialized.value) {
      await loadSettings();
      _isInitialized.value = true;
    }
    return _isInitialized.value;
  }

  void _loadSettingsSync() {
    final String? localeString = _prefs.getString(_localeKey);
    if (localeString != null) {
      final parts = localeString.split('_');
      _locale.value = Locale(parts[0], parts.length > 1 ? parts[1] : '');
    }

    final String? themeModeString = _prefs.getString(_themeModeKey);
    if (themeModeString != null) {
      _themeMode.value = ThemeMode.values.firstWhere(
        (e) => e.toString() == themeModeString,
        orElse: () => ThemeMode.light,
      );
    }

    // _fontFamily.value = _prefs.getString(_fontFamilyKey) ?? 'MiSansNormal';

    final String? colorSchemeString = _prefs.getString(_colorSchemeKey);
    if (colorSchemeString != null) {
      _colorScheme.value = FlexScheme.values.firstWhere(
        (e) => e.toString() == colorSchemeString,
        orElse: () => FlexScheme.amber,
      );
    }

    _defaultDeleteDays.value = _prefs.getInt('defaultDeleteDays') ?? 15;

    final layoutIndex = _prefs.getInt(_timelineLayoutKey);
    _timelineLayout.value = layoutIndex != null ? TimelineLayout.values[layoutIndex] : TimelineLayout.double;
  }

  void setLocale(Locale newLocale) {
    _locale.value = newLocale;
    _prefs.setString(_localeKey, '${newLocale.languageCode}_${newLocale.countryCode}');
    Get.updateLocale(newLocale); // 立即更新语言
  }

  void setThemeMode(ThemeMode newThemeMode) {
    _themeMode.value = newThemeMode;
    _prefs.setString(_themeModeKey, newThemeMode.toString());
    Get.changeThemeMode(newThemeMode); // 立即更新主题模式
  }

  void setFontFamily(String newFontFamily) {
    // _fontFamily.value = newFontFamily;
    // _prefs.setString(_fontFamilyKey, newFontFamily);
    // 只更新亮色主题
    Get.changeTheme(lightTheme);
  }

  void setColorScheme(FlexScheme newColorScheme) {
    _colorScheme.value = newColorScheme;
    _prefs.setString(_colorSchemeKey, newColorScheme.toString());
    // 只更新亮色主题
    Get.changeTheme(lightTheme);
  }

  Future<void> setTimelineLayout(TimelineLayout layout) async {
    _timelineLayout.value = layout;
    await _prefs.setInt(_timelineLayoutKey, layout.index);
  }

  List<String> getAvailableFonts() {
    return [
      'MiSansNormal',
      'AlibabaPuHuiTi',
    ];
  }

  ThemeData get lightTheme => FlexThemeData.light(
        scheme: _colorScheme.value,
        // fontFamily: _fontFamily.value,
        subThemesData: const FlexSubThemesData(
          interactionEffects: true,
          tintedDisabledControls: true,
          blendOnColors: true,
          useTextTheme: true,
        ),
      );

  ThemeData get darkTheme => FlexThemeData.dark(
        scheme: _colorScheme.value,
        // fontFamily: _fontFamily.value,
        subThemesData: const FlexSubThemesData(
          interactionEffects: true,
          tintedDisabledControls: true,
          blendOnColors: true,
          useTextTheme: true,
          defaultRadius: 8,
          elevatedButtonSchemeColor: SchemeColor.primaryContainer,
          outlinedButtonOutlineSchemeColor: SchemeColor.primary,
          toggleButtonsSchemeColor: SchemeColor.primary,
          inputDecoratorSchemeColor: SchemeColor.primary,
          fabSchemeColor: SchemeColor.primaryContainer,
        ),
        keyColors: const FlexKeyColors(
          useSecondary: true,
          useTertiary: true,
        ),
        visualDensity: FlexColorScheme.comfortablePlatformDensity,
        useMaterial3: true,
        swapLegacyOnMaterial3: true,
        surfaceMode: FlexSurfaceMode.highScaffoldLowSurface,
        blendLevel: 20,
        appBarStyle: FlexAppBarStyle.primary,
        darkIsTrueBlack: false,
        transparentStatusBar: true,
        appBarElevation: 0.5,
      );

  Future<void> loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    // ... 加载其他设置
    _defaultDeleteDays.value = prefs.getInt('defaultDeleteDays') ?? 15;
  }

  Future<void> setDefaultDeleteDays(int days) async {
    if (days != _defaultDeleteDays.value) {
      _defaultDeleteDays.value = days;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('defaultDeleteDays', days);
    }
  }

  // 添加一个方法来获取字体的中文名称
  String getFontDisplayName(String fontFamily) {
    const fontNameMap = {
      'MiSansNormal': '米黑体',
      'AlibabaPuHuiTi': '阿里巴巴普惠体',
    };
    return fontNameMap[fontFamily] ?? fontFamily;
  }

  static const String scrollbarPositionKey = 'scrollbar_position';

  // 确保默认值为 false（滚动条在左边）
  static Future<bool> isScrollbarOnRight() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(scrollbarPositionKey) ?? false;
  }

  static Future<void> setScrollbarPosition(bool isRight) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(scrollbarPositionKey, isRight);
  }
}
