// Dart imports:
import 'dart:async';
import 'dart:developer' as developer;
import 'dart:math' as math;

// Flutter imports:
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
// Package imports:
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Project imports:
import '../../helpers/database_helper.dart';
import '../../l10n/localization.dart';
import '../../models/note.dart';
import '../../models/segment.dart';
import 'painters/mark_painter.dart';
import '../../services/ai_service.dart';
import '../../services/auth_service.dart';
import '../../services/event_bus.dart';
import 'package:get/get.dart';
import 'utils/text_utils.dart'; // 添加这一行
import 'widgets/custom_slider.dart';
import '../../widgets/feature_hint.dart';
import 'widgets/input_panels.dart';
import '../auth/login_page.dart';
import '../quiz/quiz_dialog.dart';
import '../quiz/quiz_search_result_page.dart';
import '../search/search_by_category_page.dart';
import 'widgets/note_category_panel.dart';
import 'widgets/parent_note_panel.dart';
import '../translate/translate_page.dart';
import '../../routes/app_routes.dart';
import '../../widgets/ebbinghaus_hint_dialog.dart';
import '../../config.dart';

class NoteDetailPage extends StatefulWidget {
  static const routeName = '/note';
  const NoteDetailPage({
    super.key,
    required this.note,
    required this.onNoteCreated,
    this.onTranscriptionStateChanged,
  });

  final Note note;
  final VoidCallback onNoteCreated;
  final Function(bool)? onTranscriptionStateChanged;

  @override
  NoteDetailPageState createState() => NoteDetailPageState();
}

class NoteDetailPageState extends State<NoteDetailPage>
    with TickerProviderStateMixin {
  final AIService _aiService = AIService(); // 创建 AIService 实例
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  double _fontSize = 16.0;
  final List<Color> _highlightColors = [
    Colors.yellow.withOpacity(0.3),
    Colors.green.withOpacity(0.3),
    Colors.blue.withOpacity(0.3),
    Colors.red.withOpacity(0.3),
    Colors.purple.withOpacity(0.3),
  ];

  bool _isMarkingMode = true;
  bool _isPointerDown = false;
  final List<List<Offset>> _markStrokes = [];
  late Note _note;
  late ScrollController _scrollController;
  Timer? _singleTapTimer;
  double _strokeWidth = 13.0;
  late TextPainter _textPainter;
  final List<List<StyledTextSegment>> _undoHistory = [];
  OverlayEntry? _currentOverlay;
  StreamSubscription? _favoriteChangedSubscription; // 添加这一行

  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late AnimationController _categoryPanelController;
  bool _showCategoryPanel = false;

  late AnimationController _parentNotePanelController;
  bool _showParentNotePanel = false;
  String? _parentNoteTitle;

  // 添加一个新的变量来跟踪输入面板的 OverlayEntry
  OverlayEntry? _inputPanelOverlay;

  // 添加常量定义文本区域的padding
  static const double _leftPadding = 16.0;
  static const double _rightPadding = 16.0;
  static const double _topPadding = 50.0;
  static const double _bottomPadding = 70.0;

  // 添加UI元素尺寸常量
  static const double _parentNotePanelHeight = 48.0; // 父笔记面板高度

  // 在类的顶部添加一个新的状态变量
  bool _isCategoryPanelClosed = false;

  // 在类的顶部添加一个字段来存储 Drag 对象
  Drag? _drag;

  // 在类顶部添加
  DateTime? _dragStartTime;
  Offset? _dragStartPosition;

  // 添加提示ID常量
  static const String _textSelectionHintId = 'note_detail_text_selection_hint';

  // 添加这两行
  final TextEditingController _askInputController = TextEditingController();
  final ValueNotifier<bool> _isTranscribingNotifier =
      ValueNotifier<bool>(false);

  // 在类的顶部添加新的控制器
  final TextEditingController _askHighlightedInputController =
      TextEditingController();

  // 添加输入面板管理器
  final InputPanelManager _inputPanelManager = InputPanelManager();

  // 在NoteDetailPageState类的顶部添加以下状态变量
  // 长按激活选择所需的变量
  bool _isSelectionModeActive = false; // 是否处于选择模式
  Timer? _longPressTimer; // 长按检测计时器
  Offset? _initialTouchPosition; // 初始触摸位置
  final double _longPressMovementThreshold = 10.0; // 长按时允许的移动阈值
  List<Offset> _preModeActivationPoints = []; // 存储激活前的移动点

  // 在类的顶部添加新的状态变量，用于跟踪触摸是否在高亮文字上
  StyledTextSegment? _touchedHighlightedSegment;
  TapDownDetails? _lastTapDetails;

  @override
  void dispose() {
    _scrollController.removeListener(_handleScroll);
    _singleTapTimer?.cancel();
    _longPressTimer?.cancel(); // 确保长按计时器被取消
    if (_currentOverlay != null) {
      if (_currentOverlay!.mounted) {
        _currentOverlay!.remove();
      }
      _currentOverlay = null;
    }
    _animationController.dispose();
    _categoryPanelController.dispose();
    _parentNotePanelController.dispose();
    // 确保在页面销毁时清理所有面板
    if (_inputPanelOverlay != null) {
      _inputPanelOverlay!.remove();
      _inputPanelOverlay = null;
    }
    _askInputController.dispose();
    _isTranscribingNotifier.dispose();
    _askHighlightedInputController.dispose();
    _inputPanelManager.closePanel();
    _favoriteChangedSubscription?.cancel(); // 添加这一行
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _note = widget.note;
    _loadFontSize();
    _loadStrokeWidth();
    _loadNoteContent();
    // 移除 _loadScrollbarPosition();
    _scrollController.addListener(_handleScroll);
    _isMarkingMode = true; // 设置为永远处于标记模式

    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 定义滑动动画
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1), // 从顶部移入
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _categoryPanelController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _parentNotePanelController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 初始化时直接显示面板
    _showParentNotePanel = true;
    _parentNotePanelController.value = 1.0; // 直接设置动画值为1

    // 加载父笔记标题
    _loadParentNoteTitle();

    // 延迟显示提示
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showScrollbarHint(); // 只显示第一个提示
    });

    // 添加对 NoteFavoriteChangedEvent 的监听
    _favoriteChangedSubscription =
        NoteEvents.eventBus.on<NoteFavoriteChangedEvent>().listen((event) {
      if (mounted && event.note.id == _note.id) {
        setState(() {
          _note = event.note;
        });
      }
    });
  }

  void _showScrollbarHint() {
    // 跳过滚动条提示，直接显示文本选择提示
    _showTextSelectionHint();
  }

  void _showTextSelectionHint() {
    if (!mounted) return;
    FeatureHint.show(
      context,
      HintConfig(
        id: _textSelectionHintId,
        title: AppLocalizations.instance.textSelectionHintTitle,
        // 更新提示内容，说明需要长按激活选择
        content: AppLocalizations.instance.textSelectionHintContent,
        icon: FontAwesomeIcons.highlighter,
        duration: const Duration(seconds: 8),
        showDoNotRemind: true,
        onConfirm: () {},
      ),
    );
  }

  // 加载父笔记标题
  Future<void> _loadParentNoteTitle() async {
    if (_note.parentNoteId != null) {
      final parentNote = await _databaseHelper.getNoteById(_note.parentNoteId!);
      if (parentNote != null && parentNote.isDeleted == false) {
        setState(() {
          _parentNoteTitle = parentNote.title;
          _showParentNotePanel = true;
          _parentNotePanelController.forward();
        });
      }
    }
  }

  // 加载笔记内容
  Future<void> _loadNoteContent() async {
    try {
      final updatedNote = await _databaseHelper.getNoteById(_note.id!);
      if (updatedNote != null) {
        setState(() {
          _note = updatedNote;
        });
      }
    } catch (e) {
      developer.log('加载笔记内容时出错: $e');
    }
  }

  // 载字体大小
  Future<void> _loadFontSize() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _fontSize = prefs.getDouble('fontSize') ?? 24.0;
    });
  }

  // 保存字体大小
  Future<void> _saveFontSize() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('fontSize', _fontSize);
  }

  // 加载笔画宽度
  Future<void> _loadStrokeWidth() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _strokeWidth = prefs.getDouble('strokeWidth') ?? 13.0;
    });
  }

  // 保存笔画宽度
  Future<void> _saveStrokeWidth() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('strokeWidth', _strokeWidth);
  }

  // 在类中添加一个新的方法来构建 TextSpan
  List<TextSpan> _buildTextSpans(bool isDarkMode) {
    // final appSettings = Get.find<AppSettings>();
    final baseStyle = TextStyle(
      // fontFamily: appSettings.fontFamily,
      color: isDarkMode ? Colors.white : Colors.black,
      fontSize: _fontSize,
      height: 1.8,
    );

    return _note.content.map((segment) {
      return TextSpan(
        text: segment.text,
        style: baseStyle.copyWith(
          backgroundColor: segment.isHighlighted ? segment.color : null,
          color: segment.isHighlighted
              ? baseStyle.color
              : (segment.color ??
                  baseStyle.color), // 修改这里，如果高亮，就黑色，无果不是高亮就优先使用segment的颜色
          fontSize: _fontSize + segment.fontSizeAddLevel * 2,
          fontWeight: segment.bold ? FontWeight.bold : FontWeight.normal,
          fontStyle: segment.italic ? FontStyle.italic : FontStyle.normal,
          decoration: TextUtils.combineDecorations(
            segment.strikethrough,
            segment.linkedNoteId != null,
          ),
          decorationColor: segment.linkedNoteId != null ? Colors.blue : null,
        ),
      );
    }).toList();
  }

  // 修改 _initTextPainter 方法
  void _initTextPainter(BuildContext context) {
    // 计算实际可用宽度（与RichText完全一致）
    final textWidth =
        MediaQuery.of(context).size.width - _leftPadding - _rightPadding;

    _textPainter = TextPainter(
      text: TextSpan(
          children:
              _buildTextSpans(Theme.of(context).brightness == Brightness.dark)),
      textDirection: TextDirection.ltr,
    );
    _textPainter.layout(maxWidth: textWidth);
  }

  // 修改 _buildRichText 方法
  Widget _buildRichText(bool isDarkMode) {
    return RichText(
      text: TextSpan(children: _buildTextSpans(isDarkMode)),
    );
  }

  void _navigateToLinkedNote(int linkedNoteId) async {
    // 每次点击时直接查询数据库获取最新状态
    final linkedNote = await DatabaseHelper.instance.getNoteById(linkedNoteId);
    // 检查笔记状态
    if (linkedNote?.status != 0) {
      Get.snackbar(AppLocalizations.instance.message,
          AppLocalizations.instance.noteStillGenerating);
      return;
    }
    if (!mounted) return;

    if (linkedNote != null) {
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => NoteDetailPage(
            note: linkedNote,
            onNoteCreated: () {},
          ),
        ),
      );

      if (result == true) {
        await _loadNoteContent();
        setState(() {});
      }
    } else {
      Get.snackbar(AppLocalizations.instance.message,
          AppLocalizations.instance.cannotFindLinkedNote);
    }
  }

  // 添加长按检测回调方法
  void _onLongPressDetected() {
    // 触发振动反馈
    HapticFeedback.mediumImpact();

    // 优化预激活点 - 减少点的数量
    List<Offset> optimizedPoints = [];
    if (_preModeActivationPoints.length > 30) {
      // 如果点太多，进行采样以减少点的数量
      final samplingRate = _preModeActivationPoints.length ~/ 20; // 大约采样到20个点
      for (int i = 0; i < _preModeActivationPoints.length; i += samplingRate) {
        optimizedPoints.add(_preModeActivationPoints[i]);
      }
      // 确保包含最后一个点
      if (optimizedPoints.isEmpty ||
          optimizedPoints.last != _preModeActivationPoints.last) {
        optimizedPoints.add(_preModeActivationPoints.last);
      }
    } else {
      // 如果点不多，进行简单的去除过近的点
      if (_preModeActivationPoints.isNotEmpty) {
        optimizedPoints.add(_preModeActivationPoints.first);
        for (int i = 1; i < _preModeActivationPoints.length; i++) {
          final lastPoint = optimizedPoints.last;
          final currentPoint = _preModeActivationPoints[i];
          if ((lastPoint - currentPoint).distance >= 3.0) {
            optimizedPoints.add(currentPoint);
          }
        }
      }
    }

    setState(() {
      _isSelectionModeActive = true;
      _isPointerDown = true;

      // 使用优化后的点创建新轨迹
      _markStrokes.add(optimizedPoints);
    });
  }

  void _handlePointerDown(PointerDownEvent event) {
    _dragStartTime = DateTime.now();
    _dragStartPosition = event.position;
    // 移除对滚动条区域的检查
    // _startedInScrollbarArea = _isInScrollbarArea(event.localPosition, context);

    // 移除滚动条相关代码
    // if (_startedInScrollbarArea) {
    //   return;
    // }

    // 记录初始触摸位置和预激活点
    _initialTouchPosition = event.localPosition;
    // 预分配足够大小的列表以避免频繁重新分配
    _preModeActivationPoints = [event.localPosition];

    // 检查点击位置是否有高亮文字
    final textPosition = _getTextIndexAtPosition(event.localPosition);
    if (textPosition != -1) {
      // 找到点击位置对应的文本片段
      int currentPosition = 0;
      for (var segment in _note.content) {
        int segmentEnd = currentPosition + segment.text.length;
        if (currentPosition <= textPosition && textPosition < segmentEnd) {
          if (segment.isHighlighted) {
            // 保存触摸的高亮段落和点击详情，但不立即显示面板
            _touchedHighlightedSegment = segment;
            _lastTapDetails = TapDownDetails(
              globalPosition: event.position,
              localPosition: event.localPosition,
              kind: PointerDeviceKind.touch,
            );
            return; // 找到高亮文字后直接返回，准备后续处理
          }
          break;
        }
        currentPosition = segmentEnd;
      }
    }

    // 启动长按检测计时器
    _longPressTimer?.cancel();
    _longPressTimer = Timer(Duration(milliseconds: 300), () {
      if (mounted) {
        _onLongPressDetected();
      }
    });
  }

  void _handlePointerMove(PointerMoveEvent event) {
    // 如果移动了，清除触摸高亮段落记录，并启动滚动
    if (_touchedHighlightedSegment != null && _initialTouchPosition != null) {
      double moveDistance =
          (event.localPosition - _initialTouchPosition!).distance;
      if (moveDistance > 5.0) {
        // 清除状态
        _touchedHighlightedSegment = null;
        _lastTapDetails = null;

        // 启动滚动
        _drag = _scrollController.position.drag(
          DragStartDetails(globalPosition: event.position),
          () {},
        );

        // 更新拖动
        _drag?.update(DragUpdateDetails(
          globalPosition: event.position,
          delta: Offset(0, event.delta.dy),
          primaryDelta: event.delta.dy,
        ));

        return; // 直接返回，不再执行后续代码
      }
    }

    // 如果长按计时器正在运行并且未激活选择模式，检测移动并记录点
    if (!_isSelectionModeActive &&
        _longPressTimer != null &&
        _longPressTimer!.isActive &&
        _initialTouchPosition != null) {
      // 保存移动点，不论是否超过阈值
      _preModeActivationPoints.add(event.localPosition);

      // 计算移动距离
      double moveDistance =
          (event.localPosition - _initialTouchPosition!).distance;
      if (moveDistance > _longPressMovementThreshold) {
        // 移动超过阈值，取消长按
        _longPressTimer?.cancel();
        _longPressTimer = null;

        // 开始普通滚动
        _drag ??= _scrollController.position.drag(
          DragStartDetails(globalPosition: event.position),
          () {}, // 空的 dragCancelCallback
        );
      }
    }

    // 如果不在选择模式，且没有长按计时器运行，处理正常滚动
    if (!_isSelectionModeActive &&
        (_longPressTimer == null || !_longPressTimer!.isActive)) {
      if (_drag == null) {
        _drag = _scrollController.position.drag(
          DragStartDetails(globalPosition: event.position),
          () {}, // 空的 dragCancelCallback
        );
      } else {
        _drag?.update(DragUpdateDetails(
          globalPosition: event.position,
          delta: Offset(0, event.delta.dy),
          primaryDelta: event.delta.dy,
        ));
      }
    }

    // 如果正在划线，无论在哪个区域都继续划线
    if (_isMarkingMode && _isPointerDown && _isSelectionModeActive) {
      // 仅当与上一个点距离超过阈值时才添加新点，以减少点的数量
      if (_markStrokes.isNotEmpty && _markStrokes.last.isNotEmpty) {
        final lastPoint = _markStrokes.last.last;
        final newPoint = event.localPosition;
        final distance = (lastPoint - newPoint).distance;

        // 只有当距离上一个点足够远或者是手指移动速度较快时才添加点
        if (distance >= 2.0) {
          _markStrokes.last.add(newPoint);
          // 使用 markNeedsPaint 而非完整的 setState 来触发重绘
          if (mounted) {
            setState(() {
              // 不做任何事，只触发重绘
            });
          }
        }
      } else if (_markStrokes.isNotEmpty) {
        // 如果是第一个点，直接添加
        _markStrokes.last.add(event.localPosition);
        if (mounted) {
          setState(() {
            // 不做任何事，只触发重绘
          });
        }
      }
    }
  }

  void _handlePointerUp(PointerUpEvent event) {
    // 取消长按计时器
    _longPressTimer?.cancel();
    _longPressTimer = null;

    // 处理点击高亮文字的情况
    if (_touchedHighlightedSegment != null && _lastTapDetails != null) {
      // 只有当手指抬起位置与按下位置非常接近时，才认为是点击
      double tapDistance =
          (_lastTapDetails!.localPosition - event.localPosition).distance;
      if (tapDistance < 10.0) {
        // 小阈值，允许手指轻微移动
        _showColorPicker(
            _touchedHighlightedSegment!, context, _lastTapDetails!);
      }
      _touchedHighlightedSegment = null;
      _lastTapDetails = null;
      return;
    }

    // 处理drag结束
    if (_drag != null) {
      double velocity = 0.0;
      if (_dragStartTime != null && _dragStartPosition != null) {
        final double deltaY = event.position.dy - _dragStartPosition!.dy;
        final double deltaSeconds =
            DateTime.now().difference(_dragStartTime!).inMilliseconds / 1000;
        if (deltaSeconds > 0) {
          // 限制速度在合理范围内
          velocity = (deltaY / deltaSeconds).clamp(-10000, 10000);
        }
      }

      _drag?.end(DragEndDetails(
        velocity: Velocity(pixelsPerSecond: Offset(0, velocity)),
        primaryVelocity: velocity,
      ));
      _drag = null;
    }

    // 移除滚动条区域相关代码
    // if (_startedInScrollbarArea) {
    //   ...
    // }

    // 处理选择操作
    if (_isMarkingMode && _isSelectionModeActive) {
      setState(() {
        _isPointerDown = false;
        _isSelectionModeActive = false;

        if (_markStrokes.isEmpty) return; // 添加这个检查

        if (_markStrokes.last.length == 1) {
          _markStrokes.last.add(event.localPosition + const Offset(0.1, 0.1));
        }

        // 修改这里，不再过滤超出滚动条区域的点
        List<Offset> filteredPoints = _markStrokes.last;

        if (filteredPoints.isNotEmpty) {
          _markStrokes.last = filteredPoints;
          _selectTextUnderStrokes();
        }
      });

      // 清空预激活点数组
      _preModeActivationPoints.clear();
    }

    // 清理状态
    _dragStartTime = null;
    _dragStartPosition = null;
    _initialTouchPosition = null;
    // 确保高亮文字触摸状态也被清理
    _touchedHighlightedSegment = null;
    _lastTapDetails = null;
  }

  // 选择笔画下的文本
  void _selectTextUnderStrokes() {
    if (_markStrokes.isEmpty) return;

    _undoHistory.add(List.from(_note.content));

    List<Offset> allPoints = _markStrokes.expand((stroke) => stroke).toList();
    if (allPoints.isEmpty) return;

    // 不再过滤超出滚动条区域的点
    List<Offset> filteredPoints = allPoints;

    if (filteredPoints.isEmpty) {
      _markStrokes.clear();
      return;
    }

    // 使用过滤后的点继续处理
    int minIndex = _getTextIndexAtPosition(filteredPoints.first);
    int maxIndex = _getTextIndexAtPosition(filteredPoints.last);

    // 如果任一索引为-1，表示触摸在无效区域，直接返回
    if (minIndex == -1 || maxIndex == -1) {
      _markStrokes.clear();
      return;
    }

    for (var point in filteredPoints) {
      int index = _getTextIndexAtPosition(point);
      if (index != -1) {
        // 只处理有效区域内的点
        minIndex = math.min(minIndex, index);
        maxIndex = math.max(maxIndex, index);
      }
    }

    // 确保即使是点击也会选至少一字符
    if (maxIndex == minIndex) {
      maxIndex = math.min(_totalTextLength, maxIndex + 1);
    }

    minIndex = math.max(0, minIndex + 1);
    maxIndex = math.min(_totalTextLength, maxIndex + 1);

    String fullText = _note.content.map((segment) => segment.text).join();
    String initialSelection = fullText.substring(minIndex, maxIndex);
    developer.log('初始选择: "$initialSelection"');

    // 先扩展到完整的英文单词或词组
    var (newMinIndex, newMaxIndex) =
        TextUtils.expandToCompleteWords(fullText, minIndex, maxIndex);
    String selectedText = fullText.substring(newMinIndex, newMaxIndex);
    if (selectedText != initialSelection) {
      developer.log('扩展到完整英文单词/词组: "$selectedText"');
    }

    // 直接使用扩展后的索引
    minIndex = newMinIndex;
    maxIndex = newMaxIndex;

    // 检查是否需要进一步扩展选择
    bool needExpand = false;

    // 只有当文本包含标点符号时才需要进一步扩展
    if (_containsPunctuation(selectedText)) {
      needExpand = true;
      developer.log('发现标点符号，需要扩展选择');
    }

    // 根据需要扩展选择范围
    if (needExpand) {
      int oldMinIndex = minIndex;
      int oldMaxIndex = maxIndex;
      minIndex = _expandSelectionStart(minIndex);
      maxIndex = _expandSelectionEnd(maxIndex);

      String finalSelection = fullText.substring(minIndex, maxIndex);
      if (finalSelection != selectedText) {
        developer.log('扩展前: "${fullText.substring(oldMinIndex, oldMaxIndex)}"');
        developer.log('扩展后: "$finalSelection"');
      }
    }

    // 分析选中范围内的高亮段落
    List<(int, int)> nonHighlightedRanges = [];
    int currentIndex = minIndex;
    int currentPosition = 0;

    for (var segment in _note.content) {
      int segmentStart = currentPosition;
      int segmentEnd = currentPosition + segment.text.length;

      // 如果当前段落在选中范围内
      if (segmentEnd >= minIndex && segmentStart <= maxIndex) {
        if (segment.isHighlighted) {
          // 如果之前有未高亮的范围，添加到列表
          if (currentIndex < segmentStart) {
            nonHighlightedRanges.add((currentIndex, segmentStart));
          }
          // 更新当前位置到高亮段落之后
          currentIndex = segmentEnd;
        }
      }
      currentPosition = segmentEnd;
    }

    // ���加最�����一个未高亮范围（如果有）
    if (currentIndex < maxIndex) {
      nonHighlightedRanges.add((currentIndex, maxIndex));
    }

    // 如果没有可高亮的范围，直接返回
    if (nonHighlightedRanges.isEmpty) {
      _markStrokes.clear();
      return;
    }

    // 为每个未高亮范围创建新的高亮组
    List<StyledTextSegment> updatedContent = [];
    currentPosition = 0;

    // 为新的高亮组选择一个颜色
    Set<Color> adjacentColors = {};

    // 查找相邻的已有高亮组的颜色
    for (var segment in _note.content) {
      if (segment.isHighlighted &&
          (segment.text.isNotEmpty &&
              (currentPosition + segment.text.length >= minIndex ||
                  currentPosition <= maxIndex))) {
        adjacentColors.add(segment.color!);
      }
      currentPosition += segment.text.length;
    }

    // 选择一个不同的颜色
    List<Color> availableColors = List.from(_highlightColors);
    availableColors.removeWhere((color) => adjacentColors.contains(color));
    Color groupColor = availableColors.isNotEmpty
        ? availableColors.first
        : _highlightColors.first;

    // 生成新的highlightGroupId
    String highlightGroupId = DateTime.now().millisecondsSinceEpoch.toString();

    currentPosition = 0;
    for (var segment in _note.content) {
      int segmentStart = currentPosition;
      int segmentEnd = currentPosition + segment.text.length;

      if (nonHighlightedRanges
          .any((range) => range.$1 < segmentEnd && range.$2 > segmentStart)) {
        // 这个段落包含需要高亮的部分
        if (segmentStart < nonHighlightedRanges.first.$1) {
          // 添加段落开始到第一个高亮范围前的部分
          updatedContent.add(StyledTextSegment(
            text: segment.text
                .substring(0, nonHighlightedRanges.first.$1 - segmentStart),
            isHighlighted: segment.isHighlighted,
            linkedNoteId: segment.linkedNoteId,
            color: segment.color,
            bold: segment.bold,
            italic: segment.italic,
            strikethrough: segment.strikethrough,
            fontSizeAddLevel: segment.fontSizeAddLevel,
            highlightGroupId: segment.highlightGroupId,
          ));
        }

        // 处理每个需要高亮的范围
        for (var range in nonHighlightedRanges) {
          if (range.$2 <= segmentStart || range.$1 >= segmentEnd) continue;

          // 计算这个范围在当前段落中的实际范围
          int rangeStart = math.max(range.$1, segmentStart);
          int rangeEnd = math.min(range.$2, segmentEnd);

          // 使用组的颜色和ID
          updatedContent.add(StyledTextSegment(
            text: segment.text.substring(
              rangeStart - segmentStart,
              rangeEnd - segmentStart,
            ),
            isHighlighted: true,
            linkedNoteId: segment.linkedNoteId,
            color: groupColor, // 使用组的颜色
            bold: segment.bold,
            italic: segment.italic,
            strikethrough: segment.strikethrough,
            fontSizeAddLevel: segment.fontSizeAddLevel,
            highlightGroupId: highlightGroupId, // 使用组的ID
          ));
        }

        if (segmentEnd > nonHighlightedRanges.last.$2) {
          // 添加最后一个高亮范围后到段落结束的部分
          updatedContent.add(StyledTextSegment(
            text: segment.text
                .substring(nonHighlightedRanges.last.$2 - segmentStart),
            isHighlighted: segment.isHighlighted,
            linkedNoteId: segment.linkedNoteId,
            color: segment.color,
            bold: segment.bold,
            italic: segment.italic,
            strikethrough: segment.strikethrough,
            fontSizeAddLevel: segment.fontSizeAddLevel,
            highlightGroupId: segment.highlightGroupId,
          ));
        }
      } else {
        // 这个段落不需要修改，直接添加
        updatedContent.add(segment);
      }

      currentPosition = segmentEnd;
    }

    setState(() {
      _note = _note.copyWith(
        content: updatedContent,
        updatedAt: DateTime.now(),
      );
    });

    _updateNoteInDatabase();
    _markStrokes.clear();

    // 找到新高亮的第一个片段
    StyledTextSegment? newHighlightedSegment;
    for (var segment in updatedContent) {
      if (segment.highlightGroupId == highlightGroupId) {
        newHighlightedSegment = segment;
        break;
      }
    }

    // 如果找到了新高亮的片段，显示颜色选择面板
    if (newHighlightedSegment != null) {
      // 使用一个短暂的延迟，确保状态更新完成
      Future.delayed(Duration(milliseconds: 200), () {
        if (mounted) {
          _showColorPicker(
            newHighlightedSegment!,
            context,
            TapDownDetails(
              globalPosition: Offset.zero, // 这里的具体位置不重要，因为面板位置是固定的
              localPosition: Offset.zero,
              kind: PointerDeviceKind.touch,
            ),
          );
        }
      });
    }
  }

  // 获取指定位置的文本索引
  int _getTextIndexAtPosition(Offset position) {
    // 计算文本实际宽度
    final textWidth =
        MediaQuery.of(context).size.width - _leftPadding - _rightPadding;

    // 重新初始化文本绘制器，使用正确的宽度
    _textPainter = TextPainter(
      text: TextSpan(
        children: _note.content.map((segment) {
          return TextSpan(
            text: segment.text,
            style: TextStyle(
              fontSize: _fontSize + segment.fontSizeAddLevel * 2,
              height: 1.8,
              fontWeight: segment.bold ? FontWeight.bold : FontWeight.normal,
              fontStyle: segment.italic ? FontStyle.italic : FontStyle.normal,
              backgroundColor: segment.isHighlighted ? segment.color : null,
              decoration: TextUtils.combineDecorations(
                segment.strikethrough,
                segment.linkedNoteId != null,
              ),
            ),
          );
        }).toList(),
      ),
      textDirection: TextDirection.ltr,
    );
    _textPainter.layout(maxWidth: textWidth);

    // 修复坐标转换计算，使用常量值而非硬编码值
    final localPosition = position -
        Offset(_leftPadding,
            _leftPadding - _scrollController.offset + _parentNotePanelHeight);
    final textPosition = _textPainter.getPositionForOffset(localPosition);

    // 获取该位置之前和之后的字符边界
    final prevCharBoundary = _textPainter.getOffsetBefore(textPosition.offset);
    final nextCharBoundary = _textPainter.getOffsetAfter(textPosition.offset);

    // 计算触摸点到前一个字符边界和后一个字符边界的距离
    final distToPrev = (prevCharBoundary != null)
        ? (textPosition.offset - prevCharBoundary)
        : double.infinity;
    final distToNext = (nextCharBoundary != null)
        ? (nextCharBoundary - textPosition.offset)
        : double.infinity;

    // 选择距离更近的边界
    return (distToPrev <= distToNext)
        ? (prevCharBoundary ?? textPosition.offset)
        : textPosition.offset;
  }

  // 增加字体大小
  void _increaseFontSize() {
    setState(() {
      _fontSize = (_fontSize + 2).clamp(12.0, 32.0);
    });
    _saveFontSize();
  }

  // 减小字大小
  void _decreaseFontSize() {
    setState(() {
      _fontSize = (_fontSize - 2).clamp(12.0, 32.0);
    });
    _saveFontSize();
  }

  // 更新数据库中的笔记
  void _updateNoteInDatabase() async {
    await DatabaseHelper.instance.updateNote(_note);
  }

  void _showColorPicker(StyledTextSegment segment, BuildContext context,
      TapDownDetails details) async {
    // 如果已经有面板打开，先关闭它
    if (_currentOverlay != null) {
      if (_currentOverlay!.mounted) {
        _currentOverlay!.remove();
      }
      _currentOverlay = null;
    }

    // 获取当前高亮组的所有字
    String fullHighlightedText;
    if (segment.highlightGroupId != null) {
      fullHighlightedText = _note.content
          .where((seg) => seg.highlightGroupId == segment.highlightGroupId)
          .map((seg) => seg.text)
          .join('');
    } else {
      fullHighlightedText = segment.text;
    }

    List<Color> availableColors = List.from(_highlightColors);

    // 获取当前���段所在组的颜色和相邻组的颜色
    Set<Color> excludeColors = {};

    // 如果当前片段属于某个组，把这个组的颜色添加到排除列表
    if (segment.highlightGroupId != null) {
      excludeColors.add(segment.color!);
    }

    // 查找相邻的其他组的颜色
    String? currentGroupId;
    bool foundCurrentSegment = false;

    for (var seg in _note.content) {
      if (seg == segment) {
        foundCurrentSegment = true;
        currentGroupId = seg.highlightGroupId;
        continue;
      }

      if (seg.isHighlighted && seg.highlightGroupId != currentGroupId) {
        // 在找到当前片段之前，记录前一个不同组的颜色
        if (!foundCurrentSegment && seg.highlightGroupId != currentGroupId) {
          excludeColors.add(seg.color!);
          currentGroupId = seg.highlightGroupId;
        }
        // 在找到当前片段之后，记录后一个不同组的颜色并退出
        else if (foundCurrentSegment &&
            seg.highlightGroupId != currentGroupId) {
          excludeColors.add(seg.color!);
          break;
        }
      }
    }

    // 从可用颜色中移除需要排除的颜色
    availableColors.removeWhere((color) => excludeColors.contains(color));

    // 确保至少保留一种颜色
    if (availableColors.isEmpty) {
      availableColors.add(_highlightColors.first);
    }

    double left = 20;
    double top = 120;
    double width = MediaQuery.of(context).size.width - 40;

    late OverlayEntry overlayEntry;

    // 在插入 OverlayEntry 之前启动动画
    _animationController.reset(); // 重置画控制器
    _animationController.forward();

    // 点击高亮文字后弹出的面板
    overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                _removeOverlay();
              },
              child: Container(
                  color: const Color.fromARGB(255, 127, 122, 159)
                      .withOpacity(0.5)),
            ),
          ),
          Positioned(
            left: left,
            top: top,
            width: width,
            child: SlideTransition(
              position: _slideAnimation,
              child: Material(
                color: const Color.fromARGB(255, 242, 242, 242),
                elevation: 2,
                // 四周阴影
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                  side: BorderSide.none,
                ),
                child: Padding(
                  padding: EdgeInsets.fromLTRB(8, 8, 8, 8), // 整体内边距
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 用一个文本框显示 segment.text, 多行显示，无边框
                      TextField(
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: Colors.white,
                          border: InputBorder.none,
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide.none,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderSide: BorderSide.none,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          contentPadding:
                              EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        textAlignVertical: TextAlignVertical.center,
                        controller:
                            TextEditingController(text: fullHighlightedText),
                        maxLines: 5,
                        readOnly: true,
                      ),
                      SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: segment.linkedNoteId != null
                            ? MainAxisAlignment.spaceAround
                            : MainAxisAlignment.spaceBetween,
                        children: [
                          if (segment.linkedNoteId != null)
                            OutlinedButton(
                              onPressed: () {
                                _removeOverlay();
                                _navigateToLinkedNote(segment.linkedNoteId!);
                              },
                              style: OutlinedButton.styleFrom(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 8), // 增加间隙
                                minimumSize: Size(0, 0),
                                backgroundColor: Colors.white,
                                side: BorderSide.none,
                              ),
                              child: Padding(
                                padding: EdgeInsets.all(0),
                                child: Text(
                                  AppLocalizations.instance.openLinkedNote,
                                  style: TextStyle(fontSize: 14),
                                ),
                              ),
                            )
                          else ...[
                            // 下面是 是什么 按钮
                            _buildCompactButton(AppLocalizations.instance.what,
                                () async {
                              _removeOverlay();
                              await _checkLoginAndAskAI(
                                  AppLocalizations.instance.whatPrompt(
                                      _note.originalContent
                                          .map((seg) => seg.text)
                                          .join(''),
                                      fullHighlightedText),
                                  fullHighlightedText,
                                  segment);
                            }),
                            // 下面是 为什么 按钮
                            _buildCompactButton(AppLocalizations.instance.why,
                                () async {
                              _removeOverlay();
                              await _checkLoginAndAskAI(
                                  AppLocalizations.instance.whyPrompt(
                                      _note.originalContent
                                          .map((seg) => seg.text)
                                          .join(''),
                                      fullHighlightedText),
                                  fullHighlightedText,
                                  segment);
                            }),
                            // 下面是 怎么做 按钮
                            _buildCompactButton(
                                AppLocalizations.instance.howToDo, () async {
                              _removeOverlay();
                              await _checkLoginAndAskAI(
                                  AppLocalizations.instance.howtoPrompt(
                                      _note.originalContent
                                          .map((seg) => seg.text)
                                          .join(''),
                                      fullHighlightedText),
                                  fullHighlightedText,
                                  segment);
                            }),
                            // 下面是 更多 按钮
                            _buildCompactButton(AppLocalizations.instance.more,
                                () async {
                              _removeOverlay();
                              await _checkLoginAndAskAI(
                                  AppLocalizations.instance.morePrompt(
                                      _note.originalContent
                                          .map((seg) => seg.text)
                                          .join(''),
                                      fullHighlightedText),
                                  fullHighlightedText,
                                  segment);
                            }),
                          ],
                        ],
                      ),
                      segment.linkedNoteId != null
                          ? Container()
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // 下面是辩论按钮
                                _buildCompactButton(
                                    AppLocalizations.instance.debate, () async {
                                  _removeOverlay();
                                  await _checkLoginAndAskAI(
                                      AppLocalizations.instance.debatePrompt(
                                          _note.originalContent
                                              .map((seg) => seg.text)
                                              .join(''),
                                          fullHighlightedText),
                                      fullHighlightedText,
                                      segment);
                                }),
                                // 下面是关于高亮文字提问的按钮
                                _buildCompactButton(
                                    AppLocalizations.instance.askByMe,
                                    () async {
                                  _removeOverlay();
                                  // 打开面板请客户输入问题
                                  _showAskAboutHighlightedTextPanel(
                                      fullHighlightedText, segment);
                                },
                                    textColor: Colors.white,
                                    backgroundColor:
                                        Theme.of(context).primaryColor),
                                // 下面是举例按钮
                                _buildCompactButton(
                                    AppLocalizations.instance.example,
                                    () async {
                                  _removeOverlay();
                                  await _checkLoginAndAskAI(
                                      AppLocalizations.instance.examplePrompt(
                                          _note.originalContent
                                              .map((seg) => seg.text)
                                              .join(''),
                                          fullHighlightedText),
                                      fullHighlightedText,
                                      segment);
                                }),
                              ],
                            ),
                      SizedBox(height: 8),
                      Row(
                        children: [
                          Row(
                            children: [
                              SizedBox(
                                width: 32,
                                child: IconButton(
                                  icon: FaIcon(FontAwesomeIcons.trashCan,
                                      size: 20,
                                      color: segment.linkedNoteId == null
                                          ? const Color.fromARGB(
                                              255, 210, 11, 11)
                                          : Colors.grey),
                                  padding: EdgeInsets.all(0),
                                  constraints: BoxConstraints(
                                    minWidth: 28, // 减小最小宽度
                                    minHeight: 28, // 减小最小高度
                                  ),
                                  onPressed: () {
                                    if (segment.linkedNoteId == null) {
                                      _removeHighlight(segment);
                                      _removeOverlay();
                                    }
                                  },
                                ),
                              ),
                              SizedBox(
                                width: 32,
                                child: IconButton(
                                  icon: FaIcon(FontAwesomeIcons.solidCopy,
                                      size: 20,
                                      color: Colors.blueAccent.shade400),
                                  padding: EdgeInsets.all(0),
                                  constraints: BoxConstraints(
                                    minWidth: 28, // 减小最小宽度
                                    minHeight: 28, // 减小最小高度
                                  ),
                                  onPressed: () {
                                    Clipboard.setData(
                                        ClipboardData(text: segment.text));
                                    Share.share(segment.text);
                                    _removeOverlay();
                                  },
                                ),
                              ),
                              // 新增翻译按钮
                              SizedBox(
                                width: 32,
                                child: IconButton(
                                  icon: FaIcon(FontAwesomeIcons.language,
                                      size: 20, color: Colors.green.shade600),
                                  padding: EdgeInsets.all(0),
                                  constraints: BoxConstraints(
                                    minWidth: 28, // 减小最小宽度
                                    minHeight: 28, // 减小最小高度
                                  ),
                                  onPressed: () {
                                    _removeOverlay();
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => TranslatePage(
                                          text:
                                              fullHighlightedText, // 使用完整的高亮文本
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                          Spacer(),
                          Row(
                            children: [
                              Wrap(
                                spacing: 4, // 颜色按钮之间的水平间距
                                runSpacing: 0, // 颜色按钮之间的垂直间距
                                alignment: WrapAlignment.center,
                                children: availableColors.map((color) {
                                  return GestureDetector(
                                    onTap: () {
                                      _changeHighlightColor(segment, color);
                                      _removeOverlay();
                                    },
                                    child: Container(
                                      width: 22,
                                      height: 22,
                                      decoration: BoxDecoration(
                                        color: color,
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                            color: Colors.black, width: 1),
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );

    _currentOverlay = overlayEntry;
    Overlay.of(context).insert(overlayEntry);
  }

  Widget _buildCompactButton(String text, VoidCallback onPressed,
      {Color? textColor, Color? backgroundColor}) {
    return Padding(
      padding: EdgeInsets.fromLTRB(2, 0, 2, 0),
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          minimumSize: Size(0, 0),
          backgroundColor: backgroundColor ?? Colors.white,
          side: BorderSide.none,
        ),
        child: Text(text,
            style: TextStyle(
                fontSize: 14,
                color: textColor ?? Theme.of(context).primaryColor)),
      ),
    );
  }

  void _changeHighlightColor(StyledTextSegment segment, Color newColor) {
    setState(() {
      // 获取要修改的highlightGroupId
      final String? targetGroupId = segment.highlightGroupId;

      // 如果没有groupId，只修改单个片段
      if (targetGroupId == null) {
        int index = _note.content.indexOf(segment);
        if (index != -1) {
          _note.content[index] = StyledTextSegment(
            text: segment.text,
            isHighlighted: true,
            linkedNoteId: segment.linkedNoteId,
            color: newColor,
            bold: segment.bold,
            italic: segment.italic,
            strikethrough: segment.strikethrough,
            fontSizeAddLevel: segment.fontSizeAddLevel,
            highlightGroupId: segment.highlightGroupId, // 保持原有的highlightGroupId
          );
        }
      } else {
        // 修改所有具有相同highlightGroupId的片段颜色
        for (int i = 0; i < _note.content.length; i++) {
          if (_note.content[i].highlightGroupId == targetGroupId) {
            _note.content[i] = StyledTextSegment(
              text: _note.content[i].text,
              isHighlighted: true,
              linkedNoteId: _note.content[i].linkedNoteId,
              color: newColor,
              bold: _note.content[i].bold,
              italic: _note.content[i].italic,
              strikethrough: _note.content[i].strikethrough,
              fontSizeAddLevel: _note.content[i].fontSizeAddLevel,
              highlightGroupId: targetGroupId, // 保持原有的highlightGroupId
            );
          }
        }
      }
    });
    _updateNoteInDatabase();
  }

  void _undoLastSelection() {
    if (_undoHistory.isNotEmpty) {
      setState(() {
        _note = _note.copyWith(
          content: _undoHistory.removeLast(),
          updatedAt: DateTime.now(),
        );
      });
      _updateNoteInDatabase();
    }
  }

  // 添加新方法来移除高亮
  void _removeHighlight(StyledTextSegment segment) {
    setState(() {
      // 获取要移除的highlightGroupId
      final String? targetGroupId = segment.highlightGroupId;

      // 如果没有groupId，只移除单个片段
      if (targetGroupId == null) {
        int index = _note.content.indexOf(segment);
        if (index != -1) {
          _note.content[index] = StyledTextSegment(
            text: segment.text,
            isHighlighted: false,
            linkedNoteId: segment.linkedNoteId,
            color: null,
            bold: segment.bold,
            italic: segment.italic,
            strikethrough: segment.strikethrough,
            fontSizeAddLevel: segment.fontSizeAddLevel,
            highlightGroupId: null, // 清除highlightGroupId
          );
        }
      } else {
        // 移除所有具有相同highlightGroupId的高亮
        for (int i = 0; i < _note.content.length; i++) {
          if (_note.content[i].highlightGroupId == targetGroupId) {
            _note.content[i] = StyledTextSegment(
              text: _note.content[i].text,
              isHighlighted: false,
              linkedNoteId: _note.content[i].linkedNoteId,
              color: null,
              bold: _note.content[i].bold,
              italic: _note.content[i].italic,
              strikethrough: _note.content[i].strikethrough,
              fontSizeAddLevel: _note.content[i].fontSizeAddLevel,
              highlightGroupId: null, // 清除highlightGroupId
            );
          }
        }
      }
    });
    _updateNoteInDatabase();
  }

  Future<void> _checkLoginAndAskAI(String prompt, String fullHighlightedText,
      StyledTextSegment segment) async {
    final isLoggedIn = await AuthService().isLoggedIn();
    if (!isLoggedIn) {
      Get.toNamed(LoginPage.routeName);
      return;
    }
    // 清空撤销历史
    _undoHistory.clear();
    await _askAIAndCreateNote(fullHighlightedText, prompt, segment);
  }

  // 创建新笔记并在后台请求AI响应
  Future<int> _askAIAndCreateNote(String highlightedText, String prompt,
      StyledTextSegment triggerSegment) async {
    final localizations = AppLocalizations.instance;

    // 直接使用传入的 triggerSegment
    final String? groupId = triggerSegment.highlightGroupId;

    // 获取所有具有相同highlightGroupId的文本
    String fullHighlightedText = _note.content
        .where((segment) => segment.highlightGroupId == groupId)
        .map((segment) => segment.text)
        .join('');

    // 如果没有收集到文本，就使用原始文本
    if (fullHighlightedText.isEmpty) {
      fullHighlightedText = highlightedText;
    }

    // 1. 立即创建新笔记，此时是没有id的
    final newNote = Note(
      title: localizations.waitingForAIResponse, // 临时标题
      content: [StyledTextSegment(text: fullHighlightedText)],
      originalContent: [StyledTextSegment(text: fullHighlightedText)],
      category: _note.category,
      subCategory: _note.subCategory,
      subSubCategory: _note.subSubCategory,
      prompt: prompt,
      parentNoteId: _note.id,
      status: Note.STATUS_PENDING,
      retryCount: 0,
      errorMessage: '',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      source: Note.SOURCE_HIGHTED_TEXT,
    )..isUpdating = true;

    // 2. 保存新笔记到数据库，获取id
    final newNoteId = await DatabaseHelper.instance.insertNote(newNote);

    if (newNoteId != 0) {
      setState(() {
        // 为所有相关segment添加链接
        for (int i = 0; i < _note.content.length; i++) {
          // 只处理具有相同highlightGroupId的segment
          if (_note.content[i].highlightGroupId == groupId &&
              _note.content[i].isHighlighted) {
            final originalSegment = _note.content[i];
            _note.content[i] = StyledTextSegment(
              text: originalSegment.text,
              isHighlighted: true,
              linkedNoteId: newNoteId,
              color: originalSegment.color,
              bold: originalSegment.bold,
              italic: originalSegment.italic,
              strikethrough: originalSegment.strikethrough,
              fontSizeAddLevel: originalSegment.fontSizeAddLevel,
              highlightGroupId: groupId,
            );
          }
        }
      });

      _updateNoteInDatabase();

      // 4. 在后台请求AI响应
      _aiService.askAI(newNote.copyWith(id: newNoteId), noUpdateContent: false);

      // 5. 通知UI更新
      Get.snackbar(AppLocalizations.instance.message,
          localizations.waitingForAIResponse);

      NoteEvents.eventBus
          .fire(NoteCreatedEvent(newNote.copyWith(id: newNoteId)));
    }

    return newNoteId;
  }

  // 基于高亮文字客户自己输入问题创建新笔记并在后台请求AI响应
  Future<int> _askAIAndCreateNoteFromHighlightedText(
      {required String highlightedText,
      required String thought,
      required StyledTextSegment triggerSegment}) async {
    final localizations = AppLocalizations.instance;

    String prompt = AppLocalizations.instance.askAboutHighlightedTextPrompt(
        _note.originalContent.map((seg) => seg.text).join(''),
        highlightedText,
        thought);

    // 直接使用传入的 triggerSegment
    final String? groupId = triggerSegment.highlightGroupId;

    // 获取所有具有相同highlightGroupId的文本
    String fullHighlightedText = _note.content
        .where((segment) => segment.highlightGroupId == groupId)
        .map((segment) => segment.text)
        .join('');

    // 如果没有收集到文本，就使用原始文本
    if (fullHighlightedText.isEmpty) {
      fullHighlightedText = highlightedText;
    }

    // 1. 立即创建新笔记，此时是没有id的
    final newNote = Note(
      title: localizations.waitingForAIResponse, // 临时标题
      content: [StyledTextSegment(text: fullHighlightedText)],
      originalContent: [StyledTextSegment(text: fullHighlightedText)],
      category: _note.category,
      subCategory: _note.subCategory,
      subSubCategory: _note.subSubCategory,
      prompt: prompt,
      parentNoteId: _note.id,
      status: Note.STATUS_PENDING,
      retryCount: 0,
      errorMessage: '',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      source: Note.SOURCE_HIGHTED_TEXT,
    )..isUpdating = true;

    // 2. 保存新笔记到数据库，获取id
    final newNoteId = await DatabaseHelper.instance.insertNote(newNote);

    if (newNoteId != 0) {
      setState(() {
        // 为所有相关segment添加链接
        for (int i = 0; i < _note.content.length; i++) {
          // 只处理具有相同highlightGroupId的segment
          if (_note.content[i].highlightGroupId == groupId &&
              _note.content[i].isHighlighted) {
            final originalSegment = _note.content[i];
            _note.content[i] = StyledTextSegment(
              text: originalSegment.text,
              isHighlighted: true,
              linkedNoteId: newNoteId,
              color: originalSegment.color,
              bold: originalSegment.bold,
              italic: originalSegment.italic,
              strikethrough: originalSegment.strikethrough,
              fontSizeAddLevel: originalSegment.fontSizeAddLevel,
              highlightGroupId: groupId,
            );
          }
        }
      });

      _updateNoteInDatabase();

      // 4. 在后台请求AI响应
      _aiService.askAI(newNote.copyWith(id: newNoteId), noUpdateContent: false);

      // 5. 通知UI更新
      Get.snackbar(AppLocalizations.instance.message,
          localizations.waitingForAIResponse);

      NoteEvents.eventBus
          .fire(NoteCreatedEvent(newNote.copyWith(id: newNoteId)));
    }

    return newNoteId;
  }

  // 基于全文提问题创建新笔记并在后台请求AI响应
  Future<int> _askAIAndCreateNoteFromFullNote({required String thought}) async {
    final localizations = AppLocalizations.instance;
    String prompt = AppLocalizations.instance.askAboutFullNotePrompt(
        _note.originalContent.map((seg) => seg.text).join(''), thought);
    // 1. 立即创新记，此时是没有id的
    final newNote = Note(
      title: localizations.waitingForAIResponse, // 临时标题
      content: [StyledTextSegment(text: thought)],
      originalContent: [StyledTextSegment(text: thought)],
      category: _note.category,
      subCategory: _note.subCategory,
      subSubCategory: _note.subSubCategory,
      prompt: prompt,
      parentNoteId: _note.id,
      status: Note.STATUS_PENDING,
      retryCount: 0,
      errorMessage: '',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      source: Note.SOURCE_USER_ENTER,
    )..isUpdating = true;

    // 2. 保存新笔记到数据库，获取id
    final newNoteId = await DatabaseHelper.instance.insertNote(newNote);

    // 4. 在后台请求AI响应
    _aiService.askAI(newNote.copyWith(id: newNoteId), noUpdateContent: false);

    // 5. 通知UI更新
    Get.snackbar(
        AppLocalizations.instance.message, localizations.waitingForAIResponse);

    NoteEvents.eventBus.fire(NoteCreatedEvent(newNote.copyWith(id: newNoteId)));

    return newNoteId;
  }

  // 添加这个 getter 法来获取总文本长度
  int get _totalTextLength {
    return _note.content.fold(0, (sum, segment) => sum + segment.text.length);
  }

  int _expandSelectionStart(int minIndex) {
    String fullText = _note.content.map((segment) => segment.text).join();
    int originalIndex = minIndex;

    // 向前扩展到句子开头（遇到句号、问号、感叹号等停止）
    while (minIndex > 0) {
      String char = fullText[minIndex - 1];
      if (RegExp(r'[。.！!？?，,\n]').hasMatch(char)) {
        developer.log('向前扩展停止于: "$char" (位置: ${minIndex - 1})');
        break;
      }
      minIndex--;
    }

    if (minIndex != originalIndex) {
      developer.log('向前扩展: "${fullText.substring(minIndex, originalIndex)}"');
    }

    return minIndex;
  }

  int _expandSelectionEnd(int maxIndex) {
    String fullText = _note.content.map((segment) => segment.text).join();
    int originalIndex = maxIndex;

    // 向后扩展到句子结尾（直到遇到句号、问号、感叹号等）
    while (maxIndex < fullText.length) {
      String char = fullText[maxIndex];
      if (RegExp(r'[。.！!？?，,\n]').hasMatch(char)) {
        developer.log('向后扩展停止于: "$char" (位置: $maxIndex)');
        maxIndex++; // 包含句子结束符
        break;
      }
      maxIndex++;
    }

    if (maxIndex != originalIndex) {
      developer.log('向后扩展: "${fullText.substring(originalIndex, maxIndex)}"');
    }

    return maxIndex;
  }

  bool _containsPunctuation(String text) {
    // 定义需要触发扩展的标点符号
    final RegExp punctuationRegex = RegExp(r'[，,。.！!？?：:；;、…]');
    return punctuationRegex.hasMatch(text);
  }

  void _showMoreOptions() {
    // 计算距离上次正确答题的时间
    final now = DateTime.now();
    final lastCorrectTime = _note.lastCorrectTime;
    final bool withAward = lastCorrectTime == null ||
        now.difference(lastCorrectTime).inMinutes >=
            AppConfig.reviewTimeLimitMinutes;

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              ListTile(
                contentPadding: EdgeInsets.only(left: 30),
                leading: FaIcon(
                  _note.neverDelete
                      ? FontAwesomeIcons.heartCircleXmark
                      : FontAwesomeIcons.heartCirclePlus,
                  color: Colors.pink,
                ),
                title: Text(_note.neverDelete
                    ? AppLocalizations.instance.removeFavorites
                    : '${AppLocalizations.instance.favorites} - ${AppLocalizations.instance.descAddFiveAI}'),
                onTap: () {
                  Navigator.pop(context); // 关闭底部菜单
                  if (_note.neverDelete) {
                    // 如果已经收藏，直接取消收藏
                    setState(() {
                      _note = _note.copyWith(neverDelete: false);
                    });
                    _databaseHelper.updateNote(_note);
                  } else {
                    // 检查笔记是否创建超过时间限制
                    final timeDiff =
                        DateTime.now().difference(_note.createdAt).inMinutes;
                    if (timeDiff < AppConfig.quizTimeLimitMinutes) {
                      // 如果未满时间限制，显示艾宾浩斯遗忘曲线提示对话框
                      showDialog(
                        context: context,
                        builder: (context) => const EbbinghausHintDialog(),
                      );
                      return;
                    }

                    // 如果已满时间限制，显示答题对话框
                    showDialog(
                      context: context,
                      builder: (context) => QuizDialog(
                        note: _note,
                        onToggleNeverDelete: (note) {
                          setState(() {
                            _note = note;
                          });
                        },
                        withAward: true,
                      ),
                    );
                  }
                },
              ),
              const Divider(height: 0.2),
              ListTile(
                contentPadding: EdgeInsets.only(left: 32),
                leading: FaIcon(FontAwesomeIcons.language, color: Colors.green),
                title: Text(AppLocalizations.instance.translationTraining),
                onTap: () {
                  Navigator.pop(context); // 关闭菜单
                  final noteContent = _note.originalContent
                      .map((segment) => segment.text)
                      .join();
                  Get.toNamed(
                    Routes.translationTraining,
                    arguments: {'noteContent': noteContent, 'noteId': _note.id},
                  );
                },
              ),
              // 只有当笔记被收藏时才显示复习菜单
              if (_note.neverDelete) ...[
                const Divider(height: 0.2),
                ListTile(
                  contentPadding: EdgeInsets.only(left: 30),
                  leading: FaIcon(FontAwesomeIcons.bookOpen,
                      color: Colors.blueAccent),
                  title: Text(withAward
                      ? AppLocalizations.instance.reviewWithAward
                      : AppLocalizations.instance.review),
                  onTap: () {
                    Navigator.pop(context);
                    // 打开quiz搜索结果页面，显示按照noteId搜索的quiz
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => QuizSearchResultPage(
                          note: _note,
                          withAward: withAward,
                        ),
                      ),
                    );
                  },
                ),
              ],
              const Divider(height: 0.2),
              ListTile(
                contentPadding: EdgeInsets.only(left: 30),
                leading:
                    FaIcon(FontAwesomeIcons.share, color: Colors.purpleAccent),
                title: Text(AppLocalizations.instance.share),
                onTap: () {
                  Navigator.pop(context);
                  _shareNote();
                },
              ),
              const Divider(height: 0.2),
              ListTile(
                contentPadding: EdgeInsets.only(left: 30),
                leading:
                    FaIcon(FontAwesomeIcons.circleXmark, color: Colors.pink),
                title: Text(AppLocalizations.instance.clearAllHighlights),
                onTap: () {
                  Navigator.pop(context);
                  _clearAllHighlights();
                },
              ),
              // 重新获取选项
              const Divider(height: 0.2),
              if (_note.source != Note.SOURCE_CLIPBOARD && _note.prompt != '')
                ListTile(
                  contentPadding: EdgeInsets.only(left: 30),
                  leading: FaIcon(FontAwesomeIcons.rotate, color: Colors.green),
                  title: Text(AppLocalizations.instance.reGetNote),
                  onTap: () async {
                    //检查登录状态，未登录跳转登录页面
                    final isLoggedIn = await AuthService().isLoggedIn();
                    if (!isLoggedIn) {
                      Get.snackbar(AppLocalizations.instance.message,
                          AppLocalizations.instance.pleaseLoginFirst);
                      Get.toNamed(LoginPage.routeName);
                      return;
                    }
                    if (context.mounted) Navigator.pop(context); // 关闭底部菜单
                    // 显示确认对话框
                    if (context.mounted) {
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return AlertDialog(
                            title: Text(
                                AppLocalizations.instance.reGetNoteConfirm),
                            content: Text(
                                AppLocalizations.instance.reGetNoteConfirm),
                            actions: <Widget>[
                              TextButton(
                                child: Text(AppLocalizations.instance.cancel),
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                              ),
                              TextButton(
                                child: Text(AppLocalizations.instance.confirm),
                                onPressed: () {
                                  Navigator.of(context).pop(); // 关闭对话框
                                  Navigator.of(context)
                                      .pop({'action': 'retry'}); // 返回重试标志
                                },
                              ),
                            ],
                          );
                        },
                      );
                    }
                  },
                ),
              const Divider(height: 0.2),
              ListTile(
                contentPadding: EdgeInsets.only(left: 32),
                leading: FaIcon(FontAwesomeIcons.solidTrashCan),
                title: Text(AppLocalizations.instance.delete),
                onTap: () {
                  Navigator.pop(context); // 关闭底部菜单
                  _deleteNote(); // 调用删除方法
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _clearAllHighlights() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.instance.clearConfirm),
          content: Text(AppLocalizations.instance.clearConfirmDescription),
          actions: <Widget>[
            TextButton(
              child: Text(AppLocalizations.instance.cancel),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text(AppLocalizations.instance.clear),
              onPressed: () async {
                Navigator.of(context).pop();

                setState(() {
                  // 直接使用 originalContent 替换当前的 content
                  _note = _note.copyWith(
                    content: List.from(_note.originalContent),
                    updatedAt: DateTime.now(),
                  );
                });

                _updateNoteInDatabase();
              },
            ),
          ],
        );
      },
    );
  }

  void _shareNote() {
    final localizations = AppLocalizations.instance;
    String noteContent = _note.content.map((segment) => segment.text).join();
    // 把noteContent复制到剪贴板
    Clipboard.setData(ClipboardData(text: noteContent));
    Get.snackbar(
        AppLocalizations.instance.message, localizations.copiedToClipboard);
    Share.share(noteContent);
  }

  Future<void> _deleteNote() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.instance.deleteConfirm),
          content: Text(AppLocalizations.instance.deleteConfirmDescription),
          actions: <Widget>[
            TextButton(
              child: Text(AppLocalizations.instance.cancel),
              onPressed: () => Navigator.of(context).pop(false),
            ),
            TextButton(
              child: Text(AppLocalizations.instance.delete),
              onPressed: () => Navigator.of(context).pop(true),
            ),
          ],
        );
      },
    );

    if (result == true) {
      try {
        // 确保删除操作成功完成
        await _databaseHelper.deleteNote(_note.id!);

        if (!mounted) return;

        // 返回上一页并传递删除成功的标志
        Navigator.of(context).pop({'action': 'delete'}); // 返回删除标志
      } catch (e) {
        if (!mounted) return;
        Get.snackbar(AppLocalizations.instance.message,
            AppLocalizations.instance.deleteFailed);
      }
    }
  }

  // 修改滚动处理逻辑
  void _handleScroll() {
    double maxScroll = _scrollController.position.maxScrollExtent;
    double currentScroll = _scrollController.position.pixels;

    // 检查是否接近顶部
    bool shouldShowParentPanel = currentScroll <= 100;
    if (shouldShowParentPanel != _showParentNotePanel) {
      if (shouldShowParentPanel) {
        setState(() {
          _showParentNotePanel = true;
        });
        _parentNotePanelController.forward();
      } else {
        _parentNotePanelController.reverse().then((_) {
          if (mounted) {
            setState(() {
              _showParentNotePanel = false;
            });
          }
        });
      }
    }

    // 检查是否接近底部
    bool shouldShowCategoryPanel = currentScroll >= maxScroll - 100;
    bool isNearBottom = currentScroll >= maxScroll - 100;

    // 如果不在底部区域，重置面板状态
    if (!isNearBottom) {
      _isCategoryPanelClosed = false;
    }

    // 处理分类面板的显示和隐藏
    if (shouldShowCategoryPanel != _showCategoryPanel &&
        !_isCategoryPanelClosed) {
      if (shouldShowCategoryPanel) {
        setState(() {
          _showCategoryPanel = true;
        });
        _categoryPanelController.forward();
      } else {
        _categoryPanelController.reverse().then((_) {
          if (mounted) {
            setState(() {
              _showCategoryPanel = false;
            });
          }
        });
      }
    }
  }

  // 修改关闭面板的方法以使用动画
  void _removeOverlay() {
    if (_currentOverlay != null) {
      _animationController.reverse().then((_) {
        if (_currentOverlay!.mounted) {
          _currentOverlay!.remove();
        }
        _currentOverlay = null;
      });
    }
  }

  void removeOverlay() {
    if (_currentOverlay != null) {
      _animationController.reverse().then((_) {
        if (_currentOverlay!.mounted) {
          _currentOverlay!.remove();
        }
        _currentOverlay = null;
      });
    }
  }

  Widget _buildContent() {
    return Listener(
      onPointerDown: _handlePointerDown,
      onPointerMove: _handlePointerMove,
      onPointerUp: _handlePointerUp,
      child: _buildScrollView(),
    );
  }

  Widget _buildScrollView() {
    return Stack(
      children: [
        // 主要滚动内容区域
        SingleChildScrollView(
          controller: _scrollController,
          physics: const BouncingScrollPhysics(
            parent: RangeMaintainingScrollPhysics(),
            decelerationRate: ScrollDecelerationRate.fast,
          ),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height,
            ),
            child: IntrinsicHeight(
              child: Padding(
                // 修改padding让文本占据整个屏幕宽度
                padding: EdgeInsets.fromLTRB(
                    _leftPadding, _topPadding, _rightPadding, _bottomPadding),
                child: _buildRichText(
                    Theme.of(context).brightness == Brightness.dark),
              ),
            ),
          ),
        ),

        // 移除滚动条区域

        // 标记绘制层
        if (_isMarkingMode)
          CustomPaint(
            painter: MarkPainter(_markStrokes, _strokeWidth),
            size: Size.infinite,
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    _initTextPainter(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final textColor = _isMarkingMode
        ? Colors.black
        : (isDarkMode ? Colors.white : Colors.black);

    return PopScope(
      canPop: false,
      onPopInvokedWithResult:
          (bool didPop, Object? onPopInvokedWithResult) async {
        if (!didPop) {
          // 1. 如果输入面板打开着，先关闭输入面板
          if (_inputPanelManager.isPanelOpen) {
            await _inputPanelManager.closePanel();
            return;
          }

          // 2. 如果颜色选择面板打开着，关闭面板
          if (_currentOverlay != null) {
            _currentOverlay?.remove();
            _currentOverlay = null;
            return;
          }
          Navigator.pop(context);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          centerTitle: false, // 标题左对齐
          title: LayoutBuilder(
            builder: (context, constraints) {
              // 只显示第三级分类（subSubCategory）
              final displayCategory = _note.subSubCategory.isNotEmpty
                  ? _note.subSubCategory
                  : (_note.subCategory.isNotEmpty
                      ? _note.subCategory
                      : _note.category);

              return Text(
                displayCategory,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              );
            },
          ),
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
          actions: [
            IconButton(
              icon: Icon(
                FontAwesomeIcons.ellipsisVertical,
              ),
              onPressed: _showMoreOptions,
            ),
          ],
        ),
        body: Stack(
          children: [
            Column(
              children: [
                Expanded(
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      return _buildContent();
                    },
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, -5),
                      ),
                    ],
                  ),
                  child: BottomAppBar(
                    height: 60,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    shape: const CircularNotchedRectangle(),
                    notchMargin: 8,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 1,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // 字体大小调整按钮
                              IconButton(
                                icon:
                                    Icon(Icons.text_decrease, color: textColor),
                                onPressed: _decreaseFontSize,
                              ),
                              IconButton(
                                icon:
                                    Icon(Icons.text_increase, color: textColor),
                                onPressed: _increaseFontSize,
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child:
                              // 左侧滑块
                              SizedBox(
                            width: 150,
                            child: CustomSlider(
                              value: _strokeWidth,
                              min: 6.0,
                              max: 20.0,
                              onChanged: (value) {
                                setState(() {
                                  _strokeWidth = value;
                                });
                                _saveStrokeWidth();
                              },
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Container(),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            if (_isMarkingMode && _undoHistory.isNotEmpty)
              Positioned(
                right: 16,
                bottom: 135,
                child: SizedBox(
                  width: 75,
                  height: 75,
                  child: GestureDetector(
                    onTap: _undoLastSelection,
                    behavior: HitTestBehavior.opaque,
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white, // 撤销按钮始终使用白底
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            spreadRadius: 1,
                            blurRadius: 5,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(Icons.undo,
                          color:
                              Theme.of(context).colorScheme.primary, // 图标使用题色
                          size: 35),
                    ),
                  ),
                ),
              ),
            Positioned(
              left: 0,
              right: 0,
              top: 0,
              child: AnimatedBuilder(
                animation: _parentNotePanelController,
                builder: (context, child) {
                  return Visibility(
                    visible: _showParentNotePanel, // 移除 _parentNoteTitle 判断
                    maintainState: true,
                    maintainAnimation: true,
                    maintainSize: true,
                    child: FadeTransition(
                      opacity: _parentNotePanelController,
                      child: ParentNotePanel(
                        noteTitle: _note.title,
                        parentNoteTitle: _parentNoteTitle,
                        animation: _parentNotePanelController,
                        createdAt: _note.createdAt, // 添加创建时间
                        isPro: _note.isProMode, // 添加 isPro 参数
                        onClose: _hideParentNotePanel, // Add this line
                        onParentNoteTap: _parentNoteTitle != null
                            ? () async {
                                final parentNote = await _databaseHelper
                                    .getNoteById(_note.parentNoteId!);
                                if (parentNote != null) {
                                  if (context.mounted) {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => NoteDetailPage(
                                          note: parentNote,
                                          onNoteCreated: widget.onNoteCreated,
                                        ),
                                      ),
                                    );
                                  }
                                }
                              }
                            : null,
                      ),
                    ),
                  );
                },
              ),
            ),
            Positioned(
              left: 0,
              right: 0,
              bottom: 60,
              child: AnimatedBuilder(
                animation: _categoryPanelController,
                builder: (context, child) {
                  return Visibility(
                    visible: _showCategoryPanel,
                    maintainState: true,
                    maintainAnimation: true,
                    maintainSize: true,
                    child: FadeTransition(
                      opacity: _categoryPanelController,
                      child: NoteCategoryPanel(
                        categories: [
                          _note.category,
                          _note.subCategory,
                          _note.subSubCategory,
                        ],
                        onCategoryTap: _onCategoryTap,
                        onClose: _hideCategoryPanel,
                        recommendations: _note.recommendations,
                        onRecommendationTap: (text) async {
                          final localizations = AppLocalizations.instance;

                          // 添加登录检查
                          final isLoggedIn = await AuthService().isLoggedIn();
                          if (!isLoggedIn) {
                            Get.snackbar(AppLocalizations.instance.message,
                                AppLocalizations.instance.pleaseLoginFirst);
                            Get.toNamed('/login');
                            return;
                          }

                          try {
                            // 1. 创建一个临时笔记
                            List<StyledTextSegment> content = [
                              StyledTextSegment(text: text)
                            ];

                            Note note = Note(
                              title: localizations.waitingForAIResponse,
                              content: content,
                              originalContent: content,
                              prompt: AppLocalizations.instance.askPrompt(text),
                              category: '',
                              subCategory: '',
                              subSubCategory: '',
                              status: Note.STATUS_PENDING,
                              retryCount: 0,
                              errorMessage: '',
                              createdAt: DateTime.now(),
                              updatedAt: DateTime.now(),
                              source: Note.SOURCE_USER_ENTER,
                              isProMode: false,
                              recommendations: [],
                            )..isUpdating = true;

                            // 2. 保存到数据库
                            final noteId =
                                await _databaseHelper.insertNote(note);
                            note = note.copyWith(id: noteId);

                            // 3. 在后台请求AI响应
                            _aiService.askAI(note,
                                noUpdateContent: false, proMode: false);

                            // 4. 显示提示
                            Get.snackbar(
                                AppLocalizations.instance.message,
                                localizations
                                    .createdNewNoteAndWaitingForAIResponse);

                            // 5. 发送创建事件
                            NoteEvents.eventBus.fire(NoteCreatedEvent(note));
                          } catch (e) {
                            developer.log(
                                '${localizations.createNoteFailed}: ${e.toString()}');
                          }
                          // 从推荐列表中移除已点击的推荐
                          setState(() {
                            _note.recommendations.remove(text);
                          });
                          // 更新数据库
                          await DatabaseHelper.instance.updateNote(_note);
                          // 移除自动隐藏面板的逻辑，让分类部分继续显示
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        floatingActionButton: Container(
          width: 75,
          height: 75,
          margin: const EdgeInsets.only(bottom: 10),
          child: FloatingActionButton(
            heroTag: "note_detail_ask_ai_button",
            onPressed: () async {
              final isLoggedIn = await AuthService().isLoggedIn();
              if (!isLoggedIn) {
                Get.snackbar(AppLocalizations.instance.message,
                    AppLocalizations.instance.pleaseLoginFirst);
                Get.toNamed(LoginPage.routeName);
                return;
              }
              _showAskAboutFullNotePanel(
                  _note.originalContent.map((segment) => segment.text).join());
            },
            backgroundColor: Theme.of(context).colorScheme.primary,
            shape: const CircleBorder(),
            child: Icon(
              FontAwesomeIcons.circleQuestion,
              color: Colors.white,
              size: 35,
            ),
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      ),
    );
  }

  /// 显示自定义输入面板
  /// [highlightedText] - 高亮选中的文本内容
  /// [triggerSegment] - 触发显示面板的文本片段
  ///
  /// 该方法会:
  /// 1. 检查用户登录状态,未登录则跳转登录页面
  /// 2. 创建一个带动画效果的输入面板
  /// 3. 面板包含半透明背景和可输入区域
  /// 4. 点击背景可关闭面板
  /// 5. 使用 ValueNotifier 和 TweenAnimationBuilder 实现平滑动画效果
  ///
  /// 注意:
  /// - 需要在使用完毕后调用 _closeInputPanel() 关闭面板
  /// - 面板状态通过 _inputPanelOverlay 进行管理
  /// - 动画状态通过 _panelAnimationController 控制
  ///
  /// 对高亮文字提问，请客户输入问题
  Future<void> _showAskAboutHighlightedTextPanel(
      String highlightedText, StyledTextSegment triggerSegment) async {
    final isLoggedIn = await AuthService().isLoggedIn();
    if (!isLoggedIn) {
      Get.toNamed(LoginPage.routeName);
      return;
    }

    if (!mounted) return;

    _inputPanelManager.showHighlightedTextPanel(
      context: context,
      controller: _askHighlightedInputController,
      isTranscribingNotifier: _isTranscribingNotifier,
      onConfirm: (text) async {
        await _askAIAndCreateNoteFromHighlightedText(
          highlightedText: highlightedText,
          thought: text,
          triggerSegment: triggerSegment,
        );
      },
      onVoiceInput: (String text) {
        final currentPosition =
            _askHighlightedInputController.selection.baseOffset;
        final newPosition = currentPosition >= 0
            ? currentPosition
            : _askHighlightedInputController.text.length;
        final newText = _askHighlightedInputController.text.replaceRange(
          newPosition,
          newPosition,
          '${newPosition > 0 ? " " : ""}$text',
        );
        _askHighlightedInputController.value = TextEditingValue(
          text: newText,
          selection: TextSelection.collapsed(
            offset: newPosition + text.length + (newPosition > 0 ? 1 : 0),
          ),
        );
      },
    );
  }

  void _showAskAboutFullNotePanel(String fullNote) {
    _inputPanelManager.showFullNotePanel(
      context: context,
      controller: _askInputController,
      isTranscribingNotifier: _isTranscribingNotifier,
      onConfirm: (text) async {
        await _askAIAndCreateNoteFromFullNote(thought: text);
      },
      onVoiceInput: (String text) {
        final currentPosition = _askInputController.selection.baseOffset;
        final newPosition = currentPosition >= 0
            ? currentPosition
            : _askInputController.text.length;
        final newText = _askInputController.text.replaceRange(
          newPosition,
          newPosition,
          '${newPosition > 0 ? " " : ""}$text',
        );
        _askInputController.value = TextEditingValue(
          text: newText,
          selection: TextSelection.collapsed(
            offset: newPosition + text.length + (newPosition > 0 ? 1 : 0),
          ),
        );
      },
    );
  }

  void _onCategoryTap(String category) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SearchByCategoryPage(searchCategory: category),
      ),
    );
  }

  void _hideCategoryPanel() {
    setState(() {
      _isCategoryPanelClosed = true;
    });
    _categoryPanelController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _showCategoryPanel = false;
        });
      }
    });
  }

  // Add method to hide parent note panel
  void _hideParentNotePanel() {
    _parentNotePanelController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _showParentNotePanel = false;
        });
      }
    });
  }
}
