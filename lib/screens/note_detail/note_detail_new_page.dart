// Dart imports:
import 'dart:async';
import 'dart:developer' as developer;
import 'dart:math' as math;

// Flutter imports:
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:gpt_markdown/gpt_markdown.dart';

// Package imports:
import 'package:shared_preferences/shared_preferences.dart';

// Project imports:
import '../../helpers/database_helper.dart';
import '../../l10n/localization.dart';
import '../../models/note.dart';
import '../../models/segment.dart';

import '../../services/event_bus.dart';
import '../../widgets/feature_hint.dart';
import 'painters/mark_painter.dart';
import 'utils/text_utils.dart';
import 'widgets/input_panels.dart';

/// Enhanced note detail page with markdown/LaTeX rendering and touch selection
class NoteDetailNewPage extends StatefulWidget {
  static const routeName = '/note-new';

  NoteDetailNewPage({
    super.key,
    required this.note,
    required this.onNoteCreated,
    this.onTranscriptionStateChanged,
  }) {
    print('[DEBUG] NoteDetailNewPage: 构造函数被调用');
    try {
      print(
          '[DEBUG] NoteDetailNewPage: Note ID = ${note.id}, Title = ${note.title}');
      print(
          '[DEBUG] NoteDetailNewPage: onNoteCreated type = ${onNoteCreated.runtimeType}');
      print(
          '[DEBUG] NoteDetailNewPage: onTranscriptionStateChanged = $onTranscriptionStateChanged');
      print('[DEBUG] NoteDetailNewPage: 构造函数完成');
    } catch (e, stackTrace) {
      print('[ERROR] NoteDetailNewPage: 构造函数异常: $e');
      print('[ERROR] NoteDetailNewPage: 异常堆栈: $stackTrace');
    }
  }

  final Note note;
  final Future<void> Function() onNoteCreated;
  final Function(bool)? onTranscriptionStateChanged;

  @override
  NoteDetailNewPageState createState() => NoteDetailNewPageState();
}

class NoteDetailNewPageState extends State<NoteDetailNewPage>
    with TickerProviderStateMixin {
  // Core services and data
  DatabaseHelper? _databaseHelper;
  late Note _note;

  NoteDetailNewPageState() {
    print('[DEBUG] NoteDetailNewPageState: 构造函数被调用');
  }

  // Display mode management
  bool _isMarkdownMode = true; // true = markdown view, false = selection view
  late AnimationController _modeTransitionController;
  late Animation<double> _modeTransitionAnimation;

  // Font and UI settings
  double _fontSize = 16.0;
  final List<Color> _highlightColors = [
    Colors.yellow.withOpacity(0.3),
    Colors.green.withOpacity(0.3),
    Colors.blue.withOpacity(0.3),
    Colors.red.withOpacity(0.3),
    Colors.purple.withOpacity(0.3),
  ];

  // Touch selection state (for selection mode)
  bool _isMarkingMode = true;
  bool _isPointerDown = false;
  final List<List<Offset>> _markStrokes = [];
  late ScrollController _scrollController;
  double _strokeWidth = 13.0;
  OverlayEntry? _currentOverlay;
  StreamSubscription? _favoriteChangedSubscription;

  // Animation controllers
  late AnimationController _animationController;
  late AnimationController _categoryPanelController;

  late AnimationController _parentNotePanelController;
  bool _showParentNotePanel = false;
  String? _parentNoteTitle;

  // Input panel management
  OverlayEntry? _inputPanelOverlay;
  final InputPanelManager _inputPanelManager = InputPanelManager();

  // Layout constants
  static const double _leftPadding = 16.0;
  static const double _rightPadding = 16.0;
  static const double _topPadding = 50.0;
  static const double _bottomPadding = 70.0;
  static const double _parentNotePanelHeight = 48.0;

  // Selection mode state
  Drag? _drag;

  // Long press selection state
  bool _isSelectionModeActive = false;
  Timer? _longPressTimer;
  Offset? _initialTouchPosition;
  final double _longPressMovementThreshold = 10.0;
  List<Offset> _preModeActivationPoints = [];

  // Touch interaction state
  StyledTextSegment? _touchedHighlightedSegment;
  TapDownDetails? _lastTapDetails;

  // Hint management
  static const String _textSelectionHintId = 'note_detail_text_selection_hint';

  @override
  void initState() {
    print('[DEBUG] NoteDetailNewPage: initState 开始');
    super.initState();
    _note = widget.note;
    print('[DEBUG] NoteDetailNewPage: _note 设置完成');

    try {
      // Initialize DatabaseHelper
      _databaseHelper = DatabaseHelper.instance;
      print('[DEBUG] NoteDetailNewPage: _databaseHelper 初始化完成');

      _initializeControllers();
      print('[DEBUG] NoteDetailNewPage: _initializeControllers 完成');

      _loadSettings();
      print('[DEBUG] NoteDetailNewPage: _loadSettings 完成');

      _setupAnimations();
      print('[DEBUG] NoteDetailNewPage: _setupAnimations 完成');

      _setupEventListeners();
      print('[DEBUG] NoteDetailNewPage: _setupEventListeners 完成');

      _loadParentNoteTitle();
      print('[DEBUG] NoteDetailNewPage: _loadParentNoteTitle 完成');

      // Show hints after build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        print('[DEBUG] NoteDetailNewPage: 准备显示提示');
        try {
          _showScrollbarHint();
          print('[DEBUG] NoteDetailNewPage: _showScrollbarHint 完成');
        } catch (e, stackTrace) {
          print('[ERROR] NoteDetailNewPage: _showScrollbarHint 异常: $e');
          print('[ERROR] NoteDetailNewPage: 异常堆栈: $stackTrace');
        }
      });

      print('[DEBUG] NoteDetailNewPage: initState 完成');
    } catch (e, stackTrace) {
      print('[ERROR] NoteDetailNewPage: initState 异常: $e');
      print('[ERROR] NoteDetailNewPage: 异常堆栈: $stackTrace');
    }
  }

  void _initializeControllers() {
    print('[DEBUG] NoteDetailNewPageState: _initializeControllers 开始');
    try {
      _scrollController = ScrollController();
      print('[DEBUG] NoteDetailNewPageState: ScrollController 创建完成');

      _scrollController.addListener(_handleScroll);
      print('[DEBUG] NoteDetailNewPageState: ScrollController listener 添加完成');

      _isMarkingMode = true;
      print('[DEBUG] NoteDetailNewPageState: _isMarkingMode 设置完成');

      print('[DEBUG] NoteDetailNewPageState: _initializeControllers 完成');
    } catch (e, stackTrace) {
      print('[ERROR] NoteDetailNewPageState: _initializeControllers 异常: $e');
      print('[ERROR] NoteDetailNewPageState: 异常堆栈: $stackTrace');
      rethrow;
    }
  }

  Future<void> _loadSettings() async {
    await _loadFontSize();
    await _loadStrokeWidth();
    await _loadNoteContent();
  }

  void _setupAnimations() {
    // Mode transition animation
    _modeTransitionController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _modeTransitionAnimation = CurvedAnimation(
      parent: _modeTransitionController,
      curve: Curves.easeInOut,
    );

    // Animation controller for overlays
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Category panel animation
    _categoryPanelController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Parent note panel animation
    _parentNotePanelController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _showParentNotePanel = true;
    _parentNotePanelController.value = 1.0;
  }

  void _setupEventListeners() {
    _favoriteChangedSubscription =
        NoteEvents.eventBus.on<NoteFavoriteChangedEvent>().listen((event) {
      if (mounted && event.note.id == _note.id) {
        setState(() {
          _note = event.note;
        });
      }
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_handleScroll);
    _scrollController.dispose();
    _longPressTimer?.cancel();

    // Clean up overlays
    if (_currentOverlay != null) {
      if (_currentOverlay!.mounted) {
        _currentOverlay!.remove();
      }
      _currentOverlay = null;
    }
    if (_inputPanelOverlay != null) {
      _inputPanelOverlay!.remove();
      _inputPanelOverlay = null;
    }

    // Dispose animation controllers
    _modeTransitionController.dispose();
    _animationController.dispose();
    _categoryPanelController.dispose();
    _parentNotePanelController.dispose();

    // Dispose controllers (if they exist)

    // Clean up managers and subscriptions
    _inputPanelManager.closePanel();
    _favoriteChangedSubscription?.cancel();

    super.dispose();
  }

  // Settings management
  Future<void> _loadFontSize() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _fontSize = prefs.getDouble('fontSize') ?? 24.0;
    });
  }

  Future<void> _saveFontSize() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('fontSize', _fontSize);
  }

  Future<void> _loadStrokeWidth() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _strokeWidth = prefs.getDouble('strokeWidth') ?? 13.0;
    });
  }

  Future<void> _loadNoteContent() async {
    try {
      if (_databaseHelper != null) {
        final updatedNote = await _databaseHelper!.getNoteById(_note.id!);
        if (updatedNote != null) {
          setState(() {
            _note = updatedNote;
          });
        }
      }
    } catch (e) {
      developer.log('加载笔记内容时出错: $e');
    }
  }

  Future<void> _loadParentNoteTitle() async {
    if (_note.parentNoteId != null && _databaseHelper != null) {
      final parentNote =
          await _databaseHelper!.getNoteById(_note.parentNoteId!);
      if (parentNote != null && parentNote.isDeleted == false) {
        setState(() {
          _parentNoteTitle = parentNote.title;
          _showParentNotePanel = true;
          _parentNotePanelController.forward();
        });
      }
    }
  }

  void _handleScroll() {
    // Handle scroll events if needed
  }

  void _showScrollbarHint() {
    _showTextSelectionHint();
  }

  void _showTextSelectionHint() {
    if (!mounted) return;
    FeatureHint.show(
      context,
      HintConfig(
        id: _textSelectionHintId,
        title: AppLocalizations.instance.textSelectionHintTitle,
        content: AppLocalizations.instance.textSelectionHintContent,
        icon: FontAwesomeIcons.highlighter,
        duration: const Duration(seconds: 8),
        showDoNotRemind: true,
        onConfirm: () {},
      ),
    );
  }

  // Font size controls
  void _increaseFontSize() {
    setState(() {
      _fontSize = (_fontSize + 2).clamp(12.0, 32.0);
    });
    _saveFontSize();
  }

  void _decreaseFontSize() {
    setState(() {
      _fontSize = (_fontSize - 2).clamp(12.0, 32.0);
    });
    _saveFontSize();
  }

  // Mode switching
  void _toggleMode() {
    setState(() {
      _isMarkdownMode = !_isMarkdownMode;
    });

    if (_isMarkdownMode) {
      _modeTransitionController.forward();
    } else {
      _modeTransitionController.reverse();
    }
  }

  // Convert content to markdown string for gpt_markdown
  String _getMarkdownContent() {
    return _note.content.map((segment) => segment.text).join('');
  }

  // Update note in database
  void _updateNoteInDatabase() async {
    if (_databaseHelper != null) {
      await _databaseHelper!.updateNote(_note);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: _buildAppBar(),
      body: Stack(
        children: [
          // Main content area
          Column(
            children: [
              // Parent note panel
              if (_showParentNotePanel) _buildParentNotePanel(),

              // Content area
              Expanded(
                child: _buildContentArea(isDarkMode),
              ),
            ],
          ),

          // Mode toggle button
          Positioned(
            top: 16,
            right: 16,
            child: _buildModeToggleButton(),
          ),

          // Font size controls
          Positioned(
            bottom: 100,
            right: 16,
            child: _buildFontSizeControls(),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    final displayCategory = _note.subSubCategory.isNotEmpty
        ? _note.subSubCategory
        : (_note.subCategory.isNotEmpty ? _note.subCategory : _note.category);

    return AppBar(
      centerTitle: false,
      title: Text(
        displayCategory,
        style: TextStyle(
          fontSize: 16,
          color: Colors.grey[600],
        ),
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => Navigator.of(context).pop(),
      ),
      actions: [
        IconButton(
          icon: Icon(_isMarkdownMode ? Icons.edit : Icons.visibility),
          onPressed: _toggleMode,
          tooltip: _isMarkdownMode
              ? 'Switch to Selection Mode'
              : 'Switch to Markdown Mode',
        ),
      ],
    );
  }

  Widget _buildParentNotePanel() {
    return AnimatedBuilder(
      animation: _parentNotePanelController,
      builder: (context, child) {
        return SizeTransition(
          sizeFactor: _parentNotePanelController,
          child: Container(
            height: _parentNotePanelHeight,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 0.5,
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.arrow_upward,
                  size: 16,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _parentNoteTitle ?? '',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildModeToggleButton() {
    return FloatingActionButton.small(
      onPressed: _toggleMode,
      backgroundColor: Theme.of(context).primaryColor,
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: Icon(
          _isMarkdownMode ? Icons.edit : Icons.visibility,
          key: ValueKey(_isMarkdownMode),
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildFontSizeControls() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FloatingActionButton.small(
          onPressed: _increaseFontSize,
          backgroundColor: Theme.of(context).primaryColor.withOpacity(0.8),
          child: const Icon(Icons.add, color: Colors.white),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            '${_fontSize.toInt()}',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
        const SizedBox(height: 8),
        FloatingActionButton.small(
          onPressed: _decreaseFontSize,
          backgroundColor: Theme.of(context).primaryColor.withOpacity(0.8),
          child: const Icon(Icons.remove, color: Colors.white),
        ),
      ],
    );
  }

  Widget _buildContentArea(bool isDarkMode) {
    return AnimatedBuilder(
      animation: _modeTransitionAnimation,
      builder: (context, child) {
        return Stack(
          children: [
            // Markdown view
            if (_isMarkdownMode)
              Opacity(
                opacity: _modeTransitionAnimation.value,
                child: _buildMarkdownView(isDarkMode),
              ),

            // Selection view
            if (!_isMarkdownMode)
              Opacity(
                opacity: 1.0 - _modeTransitionAnimation.value,
                child: _buildSelectionView(isDarkMode),
              ),
          ],
        );
      },
    );
  }

  Widget _buildMarkdownView(bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(_leftPadding),
      child: SelectionArea(
        child: SingleChildScrollView(
          controller: _scrollController,
          child: GptMarkdown(
            _getMarkdownContent(),
            style: TextStyle(
              fontSize: _fontSize,
              height: 1.8,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
            textDirection: TextDirection.ltr,
            onLinkTap: (url, title) {
              // Handle link taps
              developer.log('Link tapped: $url');
            },
          ),
        ),
      ),
    );
  }

  Widget _buildSelectionView(bool isDarkMode) {
    return Listener(
      onPointerDown: _handlePointerDown,
      onPointerMove: _handlePointerMove,
      onPointerUp: _handlePointerUp,
      child: Stack(
        children: [
          // Text content
          Container(
            padding: const EdgeInsets.fromLTRB(
              _leftPadding,
              _topPadding,
              _rightPadding,
              _bottomPadding,
            ),
            child: SingleChildScrollView(
              controller: _scrollController,
              child: _buildRichText(isDarkMode),
            ),
          ),

          // Drawing overlay
          if (_markStrokes.isNotEmpty)
            CustomPaint(
              painter: MarkPainter(_markStrokes, _strokeWidth),
              size: Size.infinite,
            ),
        ],
      ),
    );
  }

  Widget _buildRichText(bool isDarkMode) {
    return RichText(
      text: TextSpan(children: _buildTextSpans(isDarkMode)),
    );
  }

  List<TextSpan> _buildTextSpans(bool isDarkMode) {
    final baseStyle = TextStyle(
      color: isDarkMode ? Colors.white : Colors.black,
      fontSize: _fontSize,
      height: 1.8,
    );

    return _note.content.map((segment) {
      return TextSpan(
        text: segment.text,
        style: baseStyle.copyWith(
          backgroundColor: segment.isHighlighted ? segment.color : null,
          color: segment.isHighlighted
              ? baseStyle.color
              : (segment.color ?? baseStyle.color),
          fontSize: _fontSize + segment.fontSizeAddLevel * 2,
          fontWeight: segment.bold ? FontWeight.bold : FontWeight.normal,
          fontStyle: segment.italic ? FontStyle.italic : FontStyle.normal,
          decoration: TextUtils.combineDecorations(
            segment.strikethrough,
            segment.linkedNoteId != null,
          ),
          decorationColor: segment.linkedNoteId != null ? Colors.blue : null,
        ),
      );
    }).toList();
  }

  // Touch handling methods for selection mode
  void _handlePointerDown(PointerDownEvent event) {
    if (!_isMarkingMode || _isMarkdownMode) return;

    _initialTouchPosition = event.localPosition;
    _preModeActivationPoints = [event.localPosition];

    // Check for highlighted text interaction
    final textPosition = _getTextIndexAtPosition(event.localPosition);
    if (textPosition != -1) {
      int currentPosition = 0;
      for (var segment in _note.content) {
        int segmentEnd = currentPosition + segment.text.length;
        if (currentPosition <= textPosition && textPosition < segmentEnd) {
          if (segment.isHighlighted) {
            _touchedHighlightedSegment = segment;
            _lastTapDetails = TapDownDetails(
              globalPosition: event.position,
              localPosition: event.localPosition,
              kind: PointerDeviceKind.touch,
            );
            return;
          }
          break;
        }
        currentPosition = segmentEnd;
      }
    }

    // Start long press timer for selection activation
    _longPressTimer?.cancel();
    _longPressTimer = Timer(const Duration(milliseconds: 300), () {
      if (mounted) {
        _onLongPressDetected();
      }
    });
  }

  void _handlePointerMove(PointerMoveEvent event) {
    if (!_isMarkingMode || _isMarkdownMode) return;

    // Handle highlighted text interaction
    if (_touchedHighlightedSegment != null && _initialTouchPosition != null) {
      double moveDistance =
          (event.localPosition - _initialTouchPosition!).distance;
      if (moveDistance > 5.0) {
        _touchedHighlightedSegment = null;
        _lastTapDetails = null;
        _startScrolling(event);
        return;
      }
    }

    // Handle pre-selection movement
    if (!_isSelectionModeActive &&
        _longPressTimer != null &&
        _longPressTimer!.isActive &&
        _initialTouchPosition != null) {
      _preModeActivationPoints.add(event.localPosition);

      double moveDistance =
          (event.localPosition - _initialTouchPosition!).distance;
      if (moveDistance > _longPressMovementThreshold) {
        _longPressTimer?.cancel();
        _longPressTimer = null;
        _startScrolling(event);
      }
    }

    // Handle normal scrolling
    if (!_isSelectionModeActive &&
        (_longPressTimer == null || !_longPressTimer!.isActive)) {
      _updateScrolling(event);
    }

    // Handle selection drawing
    if (_isMarkingMode && _isPointerDown && _isSelectionModeActive) {
      if (_markStrokes.isNotEmpty && _markStrokes.last.isNotEmpty) {
        final lastPoint = _markStrokes.last.last;
        final newPoint = event.localPosition;
        final distance = (lastPoint - newPoint).distance;

        if (distance >= 2.0) {
          _markStrokes.last.add(newPoint);
          setState(() {});
        }
      } else if (_markStrokes.isNotEmpty) {
        _markStrokes.last.add(event.localPosition);
        setState(() {});
      }
    }
  }

  void _handlePointerUp(PointerUpEvent event) {
    if (!_isMarkingMode || _isMarkdownMode) return;

    _longPressTimer?.cancel();
    _longPressTimer = null;

    // Handle highlighted text tap
    if (_touchedHighlightedSegment != null && _lastTapDetails != null) {
      _showHighlightedTextPanel(_touchedHighlightedSegment!, _lastTapDetails!);
      _touchedHighlightedSegment = null;
      _lastTapDetails = null;
      return;
    }

    // Handle selection mode
    if (_isSelectionModeActive) {
      _isPointerDown = false;
      if (_markStrokes.isNotEmpty) {
        _selectTextUnderStrokes();
      }
      _exitSelectionMode();
    } else {
      _stopScrolling();
    }
  }

  // Selection mode methods
  void _onLongPressDetected() {
    if (!mounted || _isSelectionModeActive) return;

    setState(() {
      _isSelectionModeActive = true;
      _isPointerDown = true;
    });

    // Add pre-activation points to current stroke
    if (_preModeActivationPoints.isNotEmpty) {
      _markStrokes.add(List.from(_preModeActivationPoints));
    }

    // Provide haptic feedback
    HapticFeedback.mediumImpact();
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionModeActive = false;
      _markStrokes.clear();
    });
  }

  // Text position utilities
  int _getTextIndexAtPosition(Offset position) {
    // Create text painter to measure text layout
    final textPainter = TextPainter(
      text: TextSpan(
          children:
              _buildTextSpans(Theme.of(context).brightness == Brightness.dark)),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout(
        maxWidth:
            MediaQuery.of(context).size.width - _leftPadding - _rightPadding);

    // Adjust position for padding and scroll offset
    final adjustedPosition = Offset(
      position.dx - _leftPadding,
      position.dy - _topPadding + _scrollController.offset,
    );

    final textPosition = textPainter.getPositionForOffset(adjustedPosition);
    return textPosition.offset;
  }

  // Text selection logic
  void _selectTextUnderStrokes() {
    if (_markStrokes.isEmpty) return;

    final selectedIndices = <int>{};

    for (final stroke in _markStrokes) {
      for (final point in stroke) {
        final index = _getTextIndexAtPosition(point);
        if (index >= 0) {
          selectedIndices.add(index);
        }
      }
    }

    if (selectedIndices.isNotEmpty) {
      _highlightSelectedText(selectedIndices);
    }
  }

  void _highlightSelectedText(Set<int> selectedIndices) {
    if (selectedIndices.isEmpty) return;

    // Find the range of selected text
    final sortedIndices = selectedIndices.toList()..sort();
    final startIndex = sortedIndices.first;
    final endIndex = sortedIndices.last;

    // Expand to complete words
    final fullText = _note.content.map((s) => s.text).join('');
    final expandedRange =
        TextUtils.expandToCompleteWords(fullText, startIndex, endIndex);

    // Apply highlighting
    _applyHighlighting(expandedRange.$1, expandedRange.$2);
  }

  void _applyHighlighting(int startIndex, int endIndex) {
    final newContent = <StyledTextSegment>[];
    int currentIndex = 0;
    final highlightColor = _highlightColors[0]; // Use first color for now
    final highlightGroupId = DateTime.now().millisecondsSinceEpoch.toString();

    for (final segment in _note.content) {
      final segmentStart = currentIndex;
      final segmentEnd = currentIndex + segment.text.length;

      if (segmentEnd <= startIndex || segmentStart >= endIndex) {
        // Segment is outside selection
        newContent.add(segment);
      } else if (segmentStart >= startIndex && segmentEnd <= endIndex) {
        // Segment is fully inside selection
        newContent.add(StyledTextSegment(
          text: segment.text,
          isHighlighted: true,
          color: highlightColor,
          highlightGroupId: highlightGroupId,
          bold: segment.bold,
          italic: segment.italic,
          strikethrough: segment.strikethrough,
          fontSizeAddLevel: segment.fontSizeAddLevel,
          linkedNoteId: segment.linkedNoteId,
        ));
      } else {
        // Segment is partially inside selection - need to split
        final selectionStart =
            math.max(startIndex, segmentStart) - segmentStart;
        final selectionEnd = math.min(endIndex, segmentEnd) - segmentStart;

        // Before selection
        if (selectionStart > 0) {
          newContent.add(StyledTextSegment(
            text: segment.text.substring(0, selectionStart),
            isHighlighted: segment.isHighlighted,
            color: segment.color,
            highlightGroupId: segment.highlightGroupId,
            bold: segment.bold,
            italic: segment.italic,
            strikethrough: segment.strikethrough,
            fontSizeAddLevel: segment.fontSizeAddLevel,
            linkedNoteId: segment.linkedNoteId,
          ));
        }

        // Selection
        newContent.add(StyledTextSegment(
          text: segment.text.substring(selectionStart, selectionEnd),
          isHighlighted: true,
          color: highlightColor,
          highlightGroupId: highlightGroupId,
          bold: segment.bold,
          italic: segment.italic,
          strikethrough: segment.strikethrough,
          fontSizeAddLevel: segment.fontSizeAddLevel,
          linkedNoteId: segment.linkedNoteId,
        ));

        // After selection
        if (selectionEnd < segment.text.length) {
          newContent.add(StyledTextSegment(
            text: segment.text.substring(selectionEnd),
            isHighlighted: segment.isHighlighted,
            color: segment.color,
            highlightGroupId: segment.highlightGroupId,
            bold: segment.bold,
            italic: segment.italic,
            strikethrough: segment.strikethrough,
            fontSizeAddLevel: segment.fontSizeAddLevel,
            linkedNoteId: segment.linkedNoteId,
          ));
        }
      }

      currentIndex = segmentEnd;
    }

    setState(() {
      _note = _note.copyWith(content: newContent);
    });

    _updateNoteInDatabase();
  }

  // Scrolling methods
  void _startScrolling(PointerEvent event) {
    _drag = _scrollController.position.drag(
      DragStartDetails(
        sourceTimeStamp: event.timeStamp,
        globalPosition: event.position,
        localPosition: event.localPosition,
      ),
      () {},
    );
  }

  void _updateScrolling(PointerEvent event) {
    _drag?.update(DragUpdateDetails(
      sourceTimeStamp: event.timeStamp,
      delta: Offset(0, -event.delta.dy),
      globalPosition: event.position,
      localPosition: event.localPosition,
    ));
  }

  void _stopScrolling() {
    _drag?.end(DragEndDetails(velocity: Velocity.zero));
    _drag = null;
  }

  // Highlighted text panel
  void _showHighlightedTextPanel(
      StyledTextSegment segment, TapDownDetails details) {
    // Remove existing overlay
    if (_currentOverlay != null) {
      _currentOverlay!.remove();
      _currentOverlay = null;
    }

    // Create new overlay
    _currentOverlay = OverlayEntry(
      builder: (context) => Positioned(
        left: details.globalPosition.dx - 100,
        top: details.globalPosition.dy - 60,
        child: Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.all(8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.psychology, size: 20),
                  onPressed: () => _askAIAboutHighlight(segment),
                  tooltip: 'Ask AI',
                ),
                IconButton(
                  icon: const Icon(Icons.translate, size: 20),
                  onPressed: () => _translateHighlight(segment),
                  tooltip: 'Translate',
                ),
                IconButton(
                  icon: const Icon(Icons.delete, size: 20),
                  onPressed: () => _removeHighlight(segment),
                  tooltip: 'Remove Highlight',
                ),
              ],
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_currentOverlay!);

    // Auto-remove after 3 seconds
    Timer(const Duration(seconds: 3), () {
      if (_currentOverlay != null && _currentOverlay!.mounted) {
        _currentOverlay!.remove();
        _currentOverlay = null;
      }
    });
  }

  // Highlight actions
  void _askAIAboutHighlight(StyledTextSegment segment) {
    _removeCurrentOverlay();
    // TODO: Implement AI question functionality
    developer.log('Ask AI about: ${segment.text}');
  }

  void _translateHighlight(StyledTextSegment segment) {
    _removeCurrentOverlay();
    // TODO: Implement translation functionality
    developer.log('Translate: ${segment.text}');
  }

  void _removeHighlight(StyledTextSegment segment) {
    _removeCurrentOverlay();

    final newContent = _note.content.map((s) {
      if (s.highlightGroupId == segment.highlightGroupId) {
        return StyledTextSegment(
          text: s.text,
          isHighlighted: false,
          color: null,
          highlightGroupId: null,
          bold: s.bold,
          italic: s.italic,
          strikethrough: s.strikethrough,
          fontSizeAddLevel: s.fontSizeAddLevel,
          linkedNoteId: s.linkedNoteId,
        );
      }
      return s;
    }).toList();

    setState(() {
      _note = _note.copyWith(content: newContent);
    });

    _updateNoteInDatabase();
  }

  void _removeCurrentOverlay() {
    if (_currentOverlay != null && _currentOverlay!.mounted) {
      _currentOverlay!.remove();
      _currentOverlay = null;
    }
  }
}
