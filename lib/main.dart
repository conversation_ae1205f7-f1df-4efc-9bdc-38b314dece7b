// Flutter imports:
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
// Package imports:
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';

// Project imports:
import 'l10n/localization.dart';
import 'routes/app_routes.dart';
import 'services/app_settings.dart';
import 'services/version_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 设置应用只支持竖屏方向
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  if (kIsWeb) {
    // 仅在 Web 平台上初始化
    databaseFactory = databaseFactoryFfiWeb;
  }

  final prefs = await SharedPreferences.getInstance();
  final appSettings = AppSettings(prefs);

  // 使用 Get.put 将 AppSettings 注入到 GetX 依赖管理中
  Get.put(appSettings, permanent: true);

  // 强制设置为亮色主题并保存到持久化存储
  appSettings.setThemeMode(ThemeMode.light);
  await prefs.setString('themeMode', 'light');

  // 初始化版本服务
  final versionService = VersionService();
  await versionService.init();

  // 在后台检查更新
  versionService.checkUpdateInBackground();

  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    systemNavigationBarColor: Colors.transparent,
    systemNavigationBarDividerColor: Colors.transparent,
    systemNavigationBarIconBrightness: Brightness.dark,
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: Brightness.dark,
    statusBarBrightness: Brightness.light,
  ));

  runApp(const MainApp());
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    // 使用 GetX 的方式获取 AppSettings
    final settings = Get.find<AppSettings>();

    return GetMaterialApp(
      title: 'Tempognize',
      debugShowCheckedModeBanner: false,
      theme: settings.lightTheme,
      darkTheme: settings.lightTheme,
      themeMode: ThemeMode.light,
      builder: (context, child) {
        // 初始化工具类
        AppLocalizations.init(context);
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            padding: MediaQuery.of(context).padding.copyWith(bottom: 0),
            viewPadding: MediaQuery.of(context).viewPadding.copyWith(bottom: 0),
            viewInsets: MediaQuery.of(context).viewInsets.copyWith(bottom: 0),
          ),
          child: child!,
        );
      },
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('zh', ''),
        Locale('en', ''),
      ],
      locale: settings.locale,
      initialRoute: Routes.splash,
      getPages: AppPages.pages,
      defaultTransition: Transition.native,
      transitionDuration: const Duration(milliseconds: 230),
      enableLog: true,
      logWriterCallback: (String text, {bool isError = false}) {
        print('[GETX ${isError ? 'ERROR' : 'LOG'}] $text');
      },
    );
  }
}
