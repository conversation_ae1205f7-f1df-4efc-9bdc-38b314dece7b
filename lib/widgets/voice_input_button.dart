// Flutter imports:
import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_sound_record/flutter_sound_record.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'dart:developer' as developer;

// Project imports:
import '../config.dart';
import '../l10n/localization.dart';
import '../services/auth_service.dart';
import 'package:get/get.dart';

class VoiceInputButton extends StatefulWidget {
  final Function(String) onTranscriptionComplete;
  final Function(bool)? onTranscriptionStateChanged;
  final double size;
  final Color? backgroundColor;
  final Color? iconColor;

  const VoiceInputButton({
    super.key,
    required this.onTranscriptionComplete,
    this.onTranscriptionStateChanged,
    this.size = 48,
    this.backgroundColor,
    this.iconColor,
  });

  @override
  State<VoiceInputButton> createState() => _VoiceInputButtonState();
}

class _VoiceInputButtonState extends State<VoiceInputButton> {
  final _recorder = FlutterSoundRecord();
  Timer? _ampTimer;
  Amplitude? _amplitude;
  bool _isRecording = false;
  String? _recordingPath;
  DateTime? _recordStartTime;
  bool _isTranscribing = false;
  bool _isOutsideFAB = false;
  Offset? _fabCenter;
  final double _fabRadius = 40.0;
  DateTime? _pressStartTime;
  bool _isLongPress = false;

  @override
  void initState() {
    super.initState();
    _checkRecordingPermission();
  }

  @override
  void dispose() {
    _recorder.dispose();
    _ampTimer?.cancel();
    super.dispose();
  }

  Future<void> _checkRecordingPermission() async {
    try {
      final hasPermission = await _recorder.hasPermission();
      if (!hasPermission) {
        await _recorder.hasPermission();
      }
    } catch (e) {
      developer.log('检查录音权限失败: $e');
    }
  }

  Future<void> _startRecording() async {
    if (_isTranscribing) return;

    if (!await _recorder.hasPermission()) {
      Get.snackbar(AppLocalizations.instance.message,
          AppLocalizations.instance.noRecordingPermission);
      return;
    }

    try {
      await _recorder.stop();

      final tempDir = await getTemporaryDirectory();
      _recordingPath = '${tempDir.path}/temp_audio.m4a';

      await _recorder.start(
        path: _recordingPath!,
        encoder: AudioEncoder.AAC,
        bitRate: 128000,
        samplingRate: 44100,
      );

      HapticFeedback.heavyImpact();
      _recordStartTime = DateTime.now();

      setState(() {
        _isRecording = true;
      });

      if (mounted) {
        _startAmplitudeTimer();
      }
    } catch (e) {
      _recordStartTime = null;
      Get.snackbar(AppLocalizations.instance.message,
          AppLocalizations.instance.recordingFailed);
    }
  }

  void _startAmplitudeTimer() {
    _ampTimer?.cancel();
    _ampTimer =
        Timer.periodic(const Duration(milliseconds: 100), (Timer t) async {
      try {
        if (!_isRecording) {
          _ampTimer?.cancel();
          return;
        }

        final amp = await _recorder.getAmplitude();

        if (_isRecording && mounted) {
          setState(() {
            _amplitude = amp;
          });
        }
      } catch (e) {
        _ampTimer?.cancel();
      }
    });
  }

  Future<void> _stopRecordingAndTranscribe() async {
    try {
      widget.onTranscriptionStateChanged?.call(true);
      setState(() => _isTranscribing = true);

      final file = File(_recordingPath!);
      if (!await file.exists()) {
        Get.snackbar(AppLocalizations.instance.message,
            AppLocalizations.instance.recordingNotFound);
        return;
      }

      final uri = Uri.parse('${AppConfig.apiBaseUrl}/audio/transcribe');
      var request = http.MultipartRequest('POST', uri);

      final token = await AuthService().getToken();
      request.headers['Authorization'] = 'Bearer $token';

      request.files.add(
        await http.MultipartFile.fromPath(
          'file',
          _recordingPath!,
          filename: 'audio.m4a',
        ),
      );

      Get.snackbar(AppLocalizations.instance.message,
          AppLocalizations.instance.transcribing);
      final response = await request.send();
      final responseStr = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        try {
          final Map<String, dynamic> jsonResponse = jsonDecode(responseStr);
          final String transcribedText = jsonResponse['text'] as String;

          if (mounted) {
            widget.onTranscriptionComplete(transcribedText);
          }
        } catch (e) {
          developer.log('JSON 解析失败: $e');
          Get.snackbar(AppLocalizations.instance.message,
              AppLocalizations.instance.transcribeFailed);
        }
      } else {
        Get.snackbar(AppLocalizations.instance.message,
            '${AppLocalizations.instance.transcribeFailed}: ${response.statusCode}');
      }
    } catch (e) {
      developer.log('转写出错: $e');
      Get.snackbar(AppLocalizations.instance.message,
          AppLocalizations.instance.transcribeFailed);
    } finally {
      if (mounted) {
        widget.onTranscriptionStateChanged?.call(false);
        setState(() => _isTranscribing = false);
      }
    }
  }

  bool _isPointInsideFAB(Offset point) {
    if (_fabCenter == null) return true;
    return (point - _fabCenter!).distance <= _fabRadius;
  }

  Widget _buildWaveform() {
    final double maxWidth = MediaQuery.of(context).size.width - 64;
    final double minWidth = maxWidth * 0.2;

    final double minDb = -160.0;
    final double maxDb = 0.0;
    final double currentDb = (_amplitude?.current ?? minDb).clamp(minDb, maxDb);
    final double normalizedAmp = (currentDb - minDb) / (maxDb - minDb);
    final double normalizedWidth =
        minWidth + (maxWidth - minWidth) * normalizedAmp;

    final Color primaryColor = _isOutsideFAB
        ? Theme.of(context).colorScheme.error
        : Theme.of(context).colorScheme.secondary;
    final Color containerColor = _isOutsideFAB
        ? Theme.of(context).colorScheme.errorContainer
        : Theme.of(context).colorScheme.secondaryContainer;

    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          const SizedBox(height: 8),
          Icon(
            FontAwesomeIcons.microphone,
            color: primaryColor,
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            AppLocalizations.instance.recording,
            style: TextStyle(
              color: primaryColor,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            height: 24,
            constraints: BoxConstraints(
              maxWidth: maxWidth,
              minWidth: minWidth,
            ),
            child: Center(
              child: Container(
                height: 24,
                width: normalizedWidth.clamp(minWidth, maxWidth),
                decoration: BoxDecoration(
                  color: containerColor,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Center(
                  child: Container(
                    height: 2,
                    width: (normalizedWidth * 0.8)
                        .clamp(minWidth * 0.8, maxWidth * 0.8),
                    decoration: BoxDecoration(
                      color: primaryColor,
                      borderRadius: BorderRadius.circular(1),
                    ),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${_amplitude?.current.toStringAsFixed(1) ?? "0.0"} dB',
            style: TextStyle(
              color: primaryColor,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            _isOutsideFAB
                ? AppLocalizations.instance.releaseToCancel
                : AppLocalizations.instance.releaseToStop,
            style: TextStyle(
              color: primaryColor,
              fontSize: 9,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        SizedBox(
          height: widget.size,
          width: widget.size,
          child: LayoutBuilder(
            builder: (context, constraints) {
              return Listener(
                onPointerDown: (PointerDownEvent event) async {
                  final RenderBox renderBox =
                      context.findRenderObject() as RenderBox;
                  _fabCenter = renderBox.localToGlobal(
                    Offset(constraints.maxWidth / 2, constraints.maxHeight / 2),
                  );

                  _pressStartTime = DateTime.now();
                  _isOutsideFAB = false;

                  await Future.delayed(const Duration(milliseconds: 200));
                  if (_pressStartTime != null) {
                    _isLongPress = true;
                    await _startRecording();
                  }
                },
                onPointerMove: (PointerMoveEvent event) {
                  if (_isRecording) {
                    final bool wasOutside = _isOutsideFAB;
                    _isOutsideFAB = !_isPointInsideFAB(event.position);

                    if (wasOutside != _isOutsideFAB) {
                      setState(() {});
                    }
                  }
                },
                onPointerUp: (PointerUpEvent event) async {
                  if (_pressStartTime == null) return;

                  final pressDuration =
                      DateTime.now().difference(_pressStartTime!);
                  _pressStartTime = null;

                  if (_isLongPress) {
                    _isLongPress = false;
                    if (_isRecording) {
                      await _recorder.stop();
                      setState(() {
                        _isRecording = false;
                        _isOutsideFAB = false;
                      });

                      if (!_isPointInsideFAB(event.position)) {
                        if (_recordingPath != null) {
                          try {
                            final file = File(_recordingPath!);
                            if (await file.exists()) {
                              await file.delete();
                            }
                          } catch (e) {
                            developer.log('删除临时录音文件失败: $e');
                          }
                        }
                        Get.snackbar(AppLocalizations.instance.message,
                            AppLocalizations.instance.recordingCancelled);
                        return;
                      }

                      if (_recordStartTime != null) {
                        final duration =
                            DateTime.now().difference(_recordStartTime!);
                        if (duration.inSeconds < 2) {
                          Get.snackbar(AppLocalizations.instance.message,
                              AppLocalizations.instance.recordingTooShort);
                          if (_recordingPath != null) {
                            try {
                              final file = File(_recordingPath!);
                              if (await file.exists()) {
                                await file.delete();
                              }
                            } catch (e) {
                              developer.log('删除临时录音文件失败: $e');
                            }
                          }
                          return;
                        }
                      }

                      await _stopRecordingAndTranscribe();
                    }
                  } else if (pressDuration.inMilliseconds < 200) {
                    Get.snackbar(AppLocalizations.instance.message,
                        AppLocalizations.instance.longPressToRecord);
                  }
                },
                onPointerCancel: (PointerCancelEvent event) async {
                  _pressStartTime = null;
                  _isLongPress = false;
                  _isOutsideFAB = false;
                  if (_isRecording) {
                    await _recorder.stop();
                    setState(() {
                      _isRecording = false;
                    });

                    if (_recordingPath != null) {
                      try {
                        final file = File(_recordingPath!);
                        if (await file.exists()) {
                          await file.delete();
                        }
                      } catch (e) {
                        developer.log('删除临时录音文件失败: $e');
                      }
                    }
                    Get.snackbar(AppLocalizations.instance.message,
                        AppLocalizations.instance.recordingCancelled);
                  }
                },
                child: FloatingActionButton(
                  heroTag: "voice_input_button",
                  onPressed: null,
                  elevation: 4.0,
                  backgroundColor: widget.backgroundColor ??
                      (_isOutsideFAB
                          ? Colors.white
                          : Theme.of(context).colorScheme.primary),
                  shape: const CircleBorder(),
                  child: _isTranscribing
                      ? SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              widget.iconColor ?? Colors.white,
                            ),
                          ),
                        )
                      : Icon(
                          FontAwesomeIcons.microphone,
                          color: widget.iconColor ??
                              (_isOutsideFAB
                                  ? Theme.of(context).colorScheme.error
                                  : Colors.white),
                          size: 36,
                        ),
                ),
              );
            },
          ),
        ),
        if (_isRecording)
          Positioned(
            bottom: widget.size + 40,
            left: -(MediaQuery.of(context).size.width - widget.size) / 2,
            child: SizedBox(
              width: MediaQuery.of(context).size.width,
              child: AnimatedOpacity(
                opacity: _isRecording ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 200),
                child: Card(
                  elevation: 8.0,
                  margin: EdgeInsets.symmetric(horizontal: 20),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: _buildWaveform(),
                ),
              ),
            ),
          ),
      ],
    );
  }
}
