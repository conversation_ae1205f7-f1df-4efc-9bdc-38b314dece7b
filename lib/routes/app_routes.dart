import 'package:get/get.dart';

// Project imports:
import '../screens/home/<USER>';
import '../screens/home/<USER>';
import '../screens/auth/login_page.dart';
import '../screens/auth/privacy_page.dart';
import '../screens/auth/terms_page.dart';
import '../screens/profile/about_page.dart';
import '../screens/profile/settings_page.dart';
import '../screens/profile/notifications_page.dart';
import '../screens/quiz/quiz_list_page.dart';
import '../screens/translation_training/translation_training_page.dart';
import '../screens/note_detail/note_detail_new_page.dart';

abstract class Routes {
  static const splash = '/splash';
  static const main = '/main';
  static const login = '/login';
  static const noteDetail = '/note-detail';
  static const noteDetailNew = '/note-detail-new';
  static const settings = '/settings';
  static const privacy = '/privacy';
  static const terms = '/terms';
  static const about = '/about';
  static const notifications = '/notifications';
  static const timeline = '/timeline';
  static const ttsTest = '/tts-test';
  static const quizList = '/quiz-list';
  static const translationTraining = '/translation-training';
}

abstract class AppPages {
  static final pages = [
    GetPage(
      name: Routes.splash,
      page: () => const SplashScreen(),
      transition: Transition.fade,
    ),
    GetPage(
      name: Routes.main,
      page: () => const MainScreen(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.login,
      page: () => const LoginPage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.settings,
      page: () => const SettingsPage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.privacy,
      page: () => const PrivacyPage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.terms,
      page: () => const TermsPage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.about,
      page: () => const AboutPage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.notifications,
      page: () => const NotificationsPage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.quizList,
      page: () => const QuizListPage(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.translationTraining,
      page: () => TranslationTrainingPage(
        noteContent: Get.arguments['noteContent'],
        noteId: Get.arguments['noteId'],
      ),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.noteDetailNew,
      page: () {
        print('[DEBUG] AppRoutes: 创建 NoteDetailNewPage');
        print('[DEBUG] AppRoutes: Get.arguments = ${Get.arguments}');

        try {
          final note = Get.arguments['note'];
          final onNoteCreated = Get.arguments['onNoteCreated'];
          final onTranscriptionStateChanged =
              Get.arguments['onTranscriptionStateChanged'];

          print('[DEBUG] AppRoutes: note = $note');
          print('[DEBUG] AppRoutes: onNoteCreated = $onNoteCreated');
          print(
              '[DEBUG] AppRoutes: onTranscriptionStateChanged = $onTranscriptionStateChanged');

          print('[DEBUG] AppRoutes: 准备创建 NoteDetailNewPage');
          final page = NoteDetailNewPage(
            note: note,
            onNoteCreated: onNoteCreated,
            onTranscriptionStateChanged: onTranscriptionStateChanged,
          );
          print('[DEBUG] AppRoutes: NoteDetailNewPage 实例创建成功');
          print('[DEBUG] AppRoutes: 准备返回页面实例');
          return page;
        } catch (e, stackTrace) {
          print('[ERROR] AppRoutes: 创建 NoteDetailNewPage 失败: $e');
          print('[ERROR] AppRoutes: 异常堆栈: $stackTrace');
          rethrow;
        }
      },
      transition: Transition.rightToLeft,
    ),
  ];
}
