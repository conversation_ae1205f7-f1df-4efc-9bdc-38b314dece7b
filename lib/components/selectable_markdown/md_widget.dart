part of 'gpt_markdown.dart';

/// It creates a markdown widget closed to each other.
class MdWidget extends StatefulWidget {
  const MdWidget(
    this.context,
    this.exp,
    this.includeGlobalComponents, {
    super.key,
    required this.config,
  });

  /// The expression to be displayed.
  final String exp;
  final BuildContext context;

  /// Whether to include global components.
  final bool includeGlobalComponents;

  /// The configuration of the markdown widget.
  final GptMarkdownConfig config;

  @override
  State<MdWidget> createState() => _MdWidgetState();
}

class _MdWidgetState extends State<MdWidget> {
  List<InlineSpan> list = [];
  @override
  void initState() {
    super.initState();
    list = MarkdownComponent.generate(
      widget.context,
      widget.exp,
      widget.config,
      widget.includeGlobalComponents,
    );
  }

  @override
  void didUpdateWidget(covariant MdWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.exp != widget.exp ||
        !oldWidget.config.isSame(widget.config)) {
      list = MarkdownComponent.generate(
        context,
        widget.exp,
        widget.config,
        widget.includeGlobalComponents,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // List<InlineSpan> list = MarkdownComponent.generate(
    //   context,
    //   widget.exp,
    //   widget.config,
    //   widget.includeGlobalComponents,
    // );
    return widget.config.getRich(
      TextSpan(children: list, style: widget.config.style?.copyWith()),
    );
  }
}

/// Selectable markdown widget with custom touch handling
class SelectableMdWidget extends StatefulWidget {
  const SelectableMdWidget(
    this.context,
    this.exp,
    this.includeGlobalComponents, {
    super.key,
    required this.config,
    this.selectionStart,
    this.selectionEnd,
    this.isSelecting = false,
  });

  /// The expression to be displayed.
  final String exp;
  final BuildContext context;

  /// Whether to include global components.
  final bool includeGlobalComponents;

  /// The configuration of the markdown widget.
  final GptMarkdownConfig config;

  /// Selection state
  final Offset? selectionStart;
  final Offset? selectionEnd;
  final bool isSelecting;

  @override
  State<SelectableMdWidget> createState() => _SelectableMdWidgetState();
}

class _SelectableMdWidgetState extends State<SelectableMdWidget> {
  List<InlineSpan> list = [];
  final GlobalKey _richTextKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    list = MarkdownComponent.generate(
      widget.context,
      widget.exp,
      widget.config,
      widget.includeGlobalComponents,
    );
  }

  @override
  void didUpdateWidget(covariant SelectableMdWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.exp != widget.exp ||
        !oldWidget.config.isSame(widget.config)) {
      list = MarkdownComponent.generate(
        context,
        widget.exp,
        widget.config,
        widget.includeGlobalComponents,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Main content
        RichText(
          key: _richTextKey,
          text:
              TextSpan(children: list, style: widget.config.style?.copyWith()),
          textDirection: widget.config.textDirection,
          textScaler: widget.config.textScaler ?? TextScaler.noScaling,
          textAlign: widget.config.textAlign ?? TextAlign.start,
          maxLines: widget.config.maxLines,
          overflow: widget.config.overflow ?? TextOverflow.clip,
        ),

        // Selection overlay
        if (widget.isSelecting &&
            widget.selectionStart != null &&
            widget.selectionEnd != null)
          CustomPaint(
            painter: SelectionPainter(
              start: widget.selectionStart!,
              end: widget.selectionEnd!,
            ),
            size: Size.infinite,
          ),
      ],
    );
  }
}

/// Custom painter for selection overlay
class SelectionPainter extends CustomPainter {
  final Offset start;
  final Offset end;

  SelectionPainter({required this.start, required this.end});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue.withOpacity(0.3)
      ..style = PaintingStyle.fill;

    final rect = Rect.fromPoints(start, end);
    canvas.drawRect(rect, paint);
  }

  @override
  bool shouldRepaint(covariant SelectionPainter oldDelegate) {
    return oldDelegate.start != start || oldDelegate.end != end;
  }
}

/// A custom table column width.
class CustomTableColumnWidth extends TableColumnWidth {
  @override
  double maxIntrinsicWidth(Iterable<RenderBox> cells, double containerWidth) {
    double width = 50;
    for (var each in cells) {
      each.layout(const BoxConstraints(), parentUsesSize: true);
      width = max(width, each.size.width);
    }
    return min(containerWidth, width);
  }

  @override
  double minIntrinsicWidth(Iterable<RenderBox> cells, double containerWidth) {
    return 50;
  }
}
