import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/rendering.dart';
import 'custom_widgets/markdown_config.dart';

import 'package:flutter/foundation.dart';
import 'package:flutter_math_fork/flutter_math.dart';
import 'custom_widgets/custom_divider.dart';
import 'custom_widgets/custom_error_image.dart';
import 'custom_widgets/custom_rb_cb.dart';
import 'custom_widgets/selectable_adapter.dart';
import 'custom_widgets/unordered_ordered_list.dart';
import 'dart:math';

import 'custom_widgets/code_field.dart';
import 'custom_widgets/indent_widget.dart';
import 'custom_widgets/link_button.dart';

part 'theme.dart';
part 'markdown_component.dart';
part 'md_widget.dart';

/// This widget create a full markdown widget with selectable text support.
class SelectableGptMarkdown extends StatefulWidget {
  const SelectableGptMarkdown(
    this.data, {
    super.key,
    this.style,
    this.followLinkColor = false,
    this.textDirection = TextDirection.ltr,
    this.latexWorkaround,
    this.textAlign,
    this.imageBuilder,
    this.textScaler,
    this.onLinkTap,
    this.latexBuilder,
    this.codeBuilder,
    this.sourceTagBuilder,
    this.highlightBuilder,
    this.linkBuilder,
    this.maxLines,
    this.overflow,
    this.orderedListBuilder,
    this.unOrderedListBuilder,
    this.tableBuilder,
    this.components,
    this.inlineComponents,
    this.useDollarSignsForLatex = false,
    this.onTextSelected,
    this.onTextHighlighted,
  });

  /// The direction of the text.
  final TextDirection textDirection;

  /// The data to be displayed.
  final String data;

  /// The style of the text.
  final TextStyle? style;

  /// The alignment of the text.
  final TextAlign? textAlign;

  /// The text scaler.
  final TextScaler? textScaler;

  /// The callback function to handle link clicks.
  final void Function(String url, String title)? onLinkTap;

  /// The LaTeX workaround.
  final String Function(String tex)? latexWorkaround;
  final int? maxLines;

  /// The overflow.
  final TextOverflow? overflow;

  /// The LaTeX builder.
  final LatexBuilder? latexBuilder;

  /// Whether to follow the link color.
  final bool followLinkColor;

  /// The code builder.
  final CodeBlockBuilder? codeBuilder;

  /// The source tag builder.
  final SourceTagBuilder? sourceTagBuilder;

  /// The highlight builder.
  final HighlightBuilder? highlightBuilder;

  /// The link builder.
  final LinkBuilder? linkBuilder;

  /// The image builder.
  final ImageBuilder? imageBuilder;

  /// The ordered list builder.
  final OrderedListBuilder? orderedListBuilder;

  /// The unordered list builder.
  final UnOrderedListBuilder? unOrderedListBuilder;

  /// Whether to use dollar signs for LaTeX.
  final bool useDollarSignsForLatex;

  /// The table builder.
  final TableBuilder? tableBuilder;

  /// The list of components.
  ///  ```dart
  /// List<MarkdownComponent> components = [
  ///   CodeBlockMd(),
  ///   NewLines(),
  ///   BlockQuote(),
  ///   ImageMd(),
  ///   ATagMd(),
  ///   TableMd(),
  ///   HTag(),
  ///   UnOrderedList(),
  ///   OrderedList(),
  ///   RadioButtonMd(),
  ///   CheckBoxMd(),
  ///   HrLine(),
  ///   StrikeMd(),
  ///   BoldMd(),
  ///   ItalicMd(),
  ///   LatexMath(),
  ///   LatexMathMultiLine(),
  ///   HighlightedText(),
  ///   SourceTag(),
  ///   IndentMd(),
  /// ];
  /// ```
  final List<MarkdownComponent>? components;

  /// The list of inline components.
  ///  ```dart
  /// List<MarkdownComponent> inlineComponents = [
  ///   ImageMd(),
  ///   ATagMd(),
  ///   TableMd(),
  ///   StrikeMd(),
  ///   BoldMd(),
  ///   ItalicMd(),
  ///   LatexMath(),
  ///   LatexMathMultiLine(),
  ///   HighlightedText(),
  ///   SourceTag(),
  /// ];
  /// ```
  final List<MarkdownComponent>? inlineComponents;

  /// Callback for text selection
  final void Function(
          String selectedText, Offset startPosition, Offset endPosition)?
      onTextSelected;

  /// Callback for text highlight
  final void Function(String highlightedText)? onTextHighlighted;

  @override
  State<SelectableGptMarkdown> createState() => _SelectableGptMarkdownState();
}

class _SelectableGptMarkdownState extends State<SelectableGptMarkdown> {
  // Selection state
  bool _isSelecting = false;
  Offset? _selectionStart;
  Offset? _selectionEnd;
  String _selectedText = '';

  // Text painter for position calculations
  late TextPainter _textPainter;

  @override
  void initState() {
    super.initState();
    _textPainter = TextPainter(
      textDirection: widget.textDirection,
    );
  }

  @override
  void dispose() {
    _textPainter.dispose();
    super.dispose();
  }

  /// A method to remove extra lines inside block LaTeX.
  // String _removeExtraLinesInsideBlockLatex(String text) {
  //   return text.replaceAllMapped(
  //     RegExp(r"\\\[(.*?)\\\]", multiLine: true, dotAll: true),
  //     (match) {
  //       String content = match[0] ?? "";
  //       return content.replaceAllMapped(RegExp(r"\n[\n\ ]+"), (match) => "\n");
  //     },
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    String tex = widget.data.trim();
    if (widget.useDollarSignsForLatex) {
      tex = tex.replaceAllMapped(
        RegExp(r"(?<!\\)\$\$(.*?)(?<!\\)\$\$", dotAll: true),
        (match) => "\\[${match[1] ?? ""}\\]",
      );
      if (!tex.contains(r"\(")) {
        tex = tex.replaceAllMapped(
          RegExp(r"(?<!\\)\$(.*?)(?<!\\)\$"),
          (match) => "\\(${match[1] ?? ""}\\)",
        );
        tex = tex.splitMapJoin(
          RegExp(r"\[.*?\]|\(.*?\)"),
          onNonMatch: (p0) {
            return p0.replaceAll("\\\$", "\$");
          },
        );
      }
    }
    // tex = _removeExtraLinesInsideBlockLatex(tex);

    return GestureDetector(
      onPanStart: _handlePanStart,
      onPanUpdate: _handlePanUpdate,
      onPanEnd: _handlePanEnd,
      onTap: _handleTap,
      child: ClipRRect(
        child: SelectableMdWidget(
          context,
          tex,
          true,
          config: GptMarkdownConfig(
            textDirection: widget.textDirection,
            style: widget.style,
            onLinkTap: widget.onLinkTap,
            textAlign: widget.textAlign,
            textScaler: widget.textScaler,
            followLinkColor: widget.followLinkColor,
            latexWorkaround: widget.latexWorkaround,
            latexBuilder: widget.latexBuilder,
            codeBuilder: widget.codeBuilder,
            maxLines: widget.maxLines,
            overflow: widget.overflow,
            sourceTagBuilder: widget.sourceTagBuilder,
            highlightBuilder: widget.highlightBuilder,
            linkBuilder: widget.linkBuilder,
            imageBuilder: widget.imageBuilder,
            orderedListBuilder: widget.orderedListBuilder,
            unOrderedListBuilder: widget.unOrderedListBuilder,
            components: widget.components,
            inlineComponents: widget.inlineComponents,
            tableBuilder: widget.tableBuilder,
          ),
          selectionStart: _selectionStart,
          selectionEnd: _selectionEnd,
          isSelecting: _isSelecting,
        ),
      ),
    );
  }

  void _handlePanStart(DragStartDetails details) {
    setState(() {
      _isSelecting = true;
      _selectionStart = details.localPosition;
      _selectionEnd = details.localPosition;
    });
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    setState(() {
      _selectionEnd = details.localPosition;
    });
  }

  void _handlePanEnd(DragEndDetails details) {
    if (_selectionStart != null && _selectionEnd != null) {
      // Calculate selected text based on positions
      _calculateSelectedText();

      // Call callback if provided
      if (widget.onTextSelected != null && _selectedText.isNotEmpty) {
        widget.onTextSelected!(_selectedText, _selectionStart!, _selectionEnd!);
      }
    }
  }

  void _handleTap() {
    setState(() {
      _isSelecting = false;
      _selectionStart = null;
      _selectionEnd = null;
      _selectedText = '';
    });
  }

  void _calculateSelectedText() {
    // TODO: Implement text selection calculation based on positions
    // This will be implemented in the next step
    _selectedText = 'Selected text placeholder';
  }
}
