// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Project imports:
import 'package:tempognize/models/note.dart';
import 'package:tempognize/models/segment.dart';
import 'package:tempognize/screens/note_detail/note_detail_new_page.dart';

void main() {
  group('NoteDetailNewPage Tests', () {
    late Note testNote;

    setUp(() {
      testNote = Note(
        id: 1,
        title: 'Test Note with Markdown',
        content: [
          StyledTextSegment(text: '# Test Markdown\n\n'),
          StyledTextSegment(
              text: 'This is a test note with **bold** text and '),
          StyledTextSegment(text: '*italic* text.\n\n'),
          StyledTextSegment(text: '## Table Example\n\n'),
          StyledTextSegment(text: '| Column 1 | Column 2 |\n'),
          StyledTextSegment(text: '|----------|----------|\n'),
          StyledTextSegment(text: '| Data 1   | Data 2   |\n\n'),
          StyledTextSegment(text: '## LaTeX Example\n\n'),
          StyledTextSegment(text: 'Inline math: \\(E = mc^2\\)\n\n'),
          StyledTextSegment(text: 'Block math:\n'),
          StyledTextSegment(text: '\\[\n'),
          StyledTextSegment(
              text: '\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}\n'),
          StyledTextSegment(text: '\\]\n'),
        ],
        originalContent: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        prompt: 'Test prompt',
        category: 'Test',
        subCategory: 'Unit Test',
        subSubCategory: 'Flutter',
      );
    });

    testWidgets('should create NoteDetailNewPage widget',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: NoteDetailNewPage(
            note: testNote,
            onNoteCreated: () async {},
          ),
        ),
      );

      // Verify the widget is created
      expect(find.byType(NoteDetailNewPage), findsOneWidget);
    });

    testWidgets('should display app bar with category',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: NoteDetailNewPage(
            note: testNote,
            onNoteCreated: () async {},
          ),
        ),
      );

      // Verify app bar shows the category
      expect(find.text('Flutter'), findsOneWidget);
    });

    testWidgets('should have mode toggle button', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: NoteDetailNewPage(
            note: testNote,
            onNoteCreated: () async {},
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify mode toggle button exists
      expect(find.byIcon(Icons.edit), findsOneWidget);
    });

    testWidgets('should have font size controls', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: NoteDetailNewPage(
            note: testNote,
            onNoteCreated: () async {},
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify font size controls exist
      expect(find.byIcon(Icons.add), findsOneWidget);
      expect(find.byIcon(Icons.remove), findsOneWidget);
    });

    testWidgets('should toggle between markdown and selection modes',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: NoteDetailNewPage(
            note: testNote,
            onNoteCreated: () async {},
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Initially should be in markdown mode (edit icon visible)
      expect(find.byIcon(Icons.edit), findsOneWidget);

      // Tap the mode toggle button
      await tester.tap(find.byIcon(Icons.edit));
      await tester.pumpAndSettle();

      // Should now be in selection mode (visibility icon visible)
      expect(find.byIcon(Icons.visibility), findsOneWidget);
    });

    testWidgets('should display markdown content in markdown mode',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: NoteDetailNewPage(
            note: testNote,
            onNoteCreated: () async {},
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Should find the GptMarkdown widget
      expect(find.byType(SelectionArea), findsOneWidget);
    });
  });

  group('Note Content Tests', () {
    testWidgets('should handle empty note content',
        (WidgetTester tester) async {
      final emptyNote = Note(
        id: 2,
        title: 'Empty Note',
        content: [],
        originalContent: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        prompt: 'Empty prompt',
        category: 'Test',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: NoteDetailNewPage(
            note: emptyNote,
            onNoteCreated: () async {},
          ),
        ),
      );

      // Should not crash with empty content
      expect(find.byType(NoteDetailNewPage), findsOneWidget);
    });

    testWidgets('should handle note with highlighted content',
        (WidgetTester tester) async {
      final highlightedNote = Note(
        id: 3,
        title: 'Highlighted Note',
        content: [
          StyledTextSegment(text: 'Normal text '),
          StyledTextSegment(
            text: 'highlighted text',
            isHighlighted: true,
            color: Colors.yellow.withOpacity(0.3),
            highlightGroupId: 'group1',
          ),
          StyledTextSegment(text: ' more normal text'),
        ],
        originalContent: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        prompt: 'Highlight test',
        category: 'Test',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: NoteDetailNewPage(
            note: highlightedNote,
            onNoteCreated: () async {},
          ),
        ),
      );

      // Should handle highlighted content without crashing
      expect(find.byType(NoteDetailNewPage), findsOneWidget);
    });
  });
}
