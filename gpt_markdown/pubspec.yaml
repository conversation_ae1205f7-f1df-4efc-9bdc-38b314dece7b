name: gpt_markdown
description: "Powerful Flutter Markdown & LaTeX Renderer: Rich Text, Math, Tables, Links, and Text Selection. Ideal for ChatGPT, Gemini, and more."
version: 1.1.2
homepage: https://github.com/Infinitix-LLC/gpt_markdown

environment:
  sdk: '>=3.7.0 <4.0.0'
  flutter: ">=3.29.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_math_fork: ^0.7.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0

flutter:
  fonts:
    - family: JetBrainsMono
      fonts:
        - asset: lib/fonts/JetBrainsMono-Regular.ttf

topics:
  - markdown
  - latex
  - selectable
  - chatgpt
  - gemini

keywords:
  - flutter
  - markdown
  - flutter markdown
  - gpt
  - latex
  - chatgpt
  - rich text
  - ai
